using System.Collections.Generic;

namespace HolyBless.Books.Dtos;

public class ChapterTreeDto
{
    public int Id { get; set; }
    public int EBookId { get; set; }
    public int? ParentChapterId { get; set; }
    public string Title { get; set; } = string.Empty;
    public string? Description { get; set; }
    public int Weight { get; set; } = 0;
    public int Views { get; set; } = 0;
    public int Likes { get; set; } = 0;
    public bool IsRoot { get; set; } = false; // Indicates if this is a root chapter
    public int ArticleCount { get; set; } = 0; // Number of articles in this chapter
    public List<ChapterTreeDto> Children { get; set; } = new List<ChapterTreeDto>();
}
