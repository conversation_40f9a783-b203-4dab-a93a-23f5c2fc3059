﻿using HolyBless.Entities.Buckets;
using System.Collections.Generic;
using Volo.Abp.Domain.Entities.Auditing;

namespace HolyBless.Entities.Books
{
    public class Chapter: FullAuditedAggregateRoot<int>
    {
        public int EBookId { get; set; }
        public EBook EBook { get; set; } = default!;
        public int? ParentChapterId { get; set; }
        public Chapter? ParentChapter { get; set; }
        public string Title { get; set; } = default!;
        public string? Description { get; set; }
        public int Weight { get; set; } = 0;
        public int Views { get; set; } = 0;
        public int Likes { get; set; } = 0;
        // The content of the chapter, could be text or HTML
        public string? Content { get; set; }
        public BucketFile? ContentAudioFile { get; set; }
        public int? ContentAudioFileId { get; set; }
        public ICollection<Chapter> ChildChapters { get; set; } = [];
        public ICollection<ChapterToArticle> ChapterToArticles { get; set; } = [];
    }
}
