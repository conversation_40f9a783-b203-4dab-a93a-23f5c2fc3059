using HolyBless.Books.Dtos;
using System.Collections.Generic;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;

namespace HolyBless.Books;

public interface IReadOnlyEBookAppService
{
    Task<EBookDto> GetAsync(int id);

    Task<PagedResultDto<EBookDto>> GetListAsync(PagedAndSortedResultRequestDto input);

    Task<List<EBookDto>> GetEBooksByChannelIdAsync(int channelId);

    Task<List<ChapterTreeDto>> GetChapterTreeByEBookIdAsync(int eBookId);
}
