﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using HolyBless.Permissions;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Repositories;
using System.Linq.Dynamic.Core;
using HolyBless.Entities.Books;
using HolyBless.Books.Dtos;

namespace HolyBless.Books;

[Authorize(HolyBlessPermissions.EBooks.Default)]
public class EBookAppService : ReadOnlyEBookAppService, IEBookAppService
{
    private readonly IRepository<EBook, int> _repository;

    public EBookAppService(
        IRepository<EBook, int> repository,
        IRepository<Chapter, int> chapterRepository,
        IRepository<ChapterToArticle> chapterToArticleRepository)
        : base(repository, chapterRepository, chapterToArticleRepository)
    {
        _repository = repository;
    }



    [Authorize(HolyBlessPermissions.EBooks.Create)]
    public async Task<EBookDto> CreateAsync(CreateUpdateEBookDto input)
    {
        var book = ObjectMapper.Map<CreateUpdateEBookDto, EBook>(input);
        await _repository.InsertAsync(book);
        return ObjectMapper.Map<EBook, EBookDto>(book);
    }

    [Authorize(HolyBlessPermissions.EBooks.Edit)]
    public async Task<EBookDto> UpdateAsync(int id, CreateUpdateEBookDto input)
    {
        var book = await _repository.GetAsync(id);
        ObjectMapper.Map(input, book);
        await _repository.UpdateAsync(book);
        return ObjectMapper.Map<EBook, EBookDto>(book);
    }

    [Authorize(HolyBlessPermissions.EBooks.Delete)]
    public async Task DeleteAsync(int id)
    {
        await _repository.DeleteAsync(id);
    }
}