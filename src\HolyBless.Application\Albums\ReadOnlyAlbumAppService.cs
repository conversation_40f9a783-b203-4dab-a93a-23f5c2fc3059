using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using HolyBless.Albums.Dtos;
using HolyBless.Buckets;
using HolyBless.Entities.Albums;
using HolyBless.Entities.Buckets;
using HolyBless.Entities.Channels;
using HolyBless.Services;
using Microsoft.EntityFrameworkCore;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Entities;
using Volo.Abp.Domain.Repositories;

namespace HolyBless.Albums
{
    public class ReadOnlyAlbumAppService(
        IRepository<Album, int> albumRepository,
        IRepository<AlbumToFile> albumToFileRepository,
        IRepository<BucketFile, int> bucketFileRepository,
        IRepository<Channel, int> channelRepository,
        IRequestContextService requestContextService,
        ICachedFileUrlAppService cachedFileUrlAppService
            ) : HolyBlessAppService, IReadOnlyAlbumAppService
    {
        protected readonly IRepository<Album, int> _albumRepository = albumRepository;
        protected readonly IRepository<AlbumToFile> _albumToFileRepository = albumToFileRepository;
        protected readonly IRepository<BucketFile, int> _bucketFileRepository = bucketFileRepository;
        protected readonly IRepository<Channel, int> _channelRepository = channelRepository;
        protected readonly IRequestContextService _requestContextService = requestContextService;
        protected readonly ICachedFileUrlAppService _cachedFileUrlAppService = cachedFileUrlAppService;

        protected readonly string _preferProvider = requestContextService.GetPreferProvider();

        [RemoteService(false)]
        public async Task<AlbumDto> GetAsync(int id)
        {
            var queryable = await _albumRepository.GetQueryableAsync();
            var album = await queryable
                .FirstOrDefaultAsync(x => x.Id == id);

            if (album == null)
            {
                throw new EntityNotFoundException(typeof(Album), id);
            }

            var rt = ObjectMapper.Map<Album, AlbumDto>(album);
            rt.ThumbnailUrl = await _cachedFileUrlAppService.GetCachedComputeUrlAsync(album.ThumbnailFileId, _preferProvider);
            return rt;
        }

        /// <summary>
        /// Get a paginated list of albums based on search criteria.
        /// When a channel source is Album, UI should call this method twice to render Album listing page
        /// One call with AlbumType:Audio and another with AlbumType:Video.
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<PagedResultDto<AlbumDto>> GetListAsync(AlbumSearchDto input)
        {
            var queryable = await _albumRepository.GetQueryableAsync();

            var query = queryable
                .WhereIf(input.ChannelId.HasValue, x => x.ChannelId == input.ChannelId)
                .WhereIf(input.AlbumType.HasValue, x => x.AlbumType == input.AlbumType)
                ;

            var totalCount = await query.CountAsync();

            var albums = await query
                .OrderBy(input.Sorting ?? "Weight desc, CreationTime desc")
                .Skip(input.SkipCount)
                .Take(input.MaxResultCount)
                .ToListAsync();

            var albumDtos = ObjectMapper.Map<List<Album>, List<AlbumDto>>(albums);
            await FillThumbnailUrl(albumDtos);

            return new PagedResultDto<AlbumDto>(totalCount, albumDtos);
        }

        protected async Task FillThumbnailUrl(List<AlbumDto> albumDtos)
        {
            var urls = await _cachedFileUrlAppService.GetCachedComputeUrlAsync(
                albumDtos.Select(x => x.ThumbnailFileId).ToList(),
                _preferProvider
            );
            foreach (var al in albumDtos)
            {
                if (al.ThumbnailFileId == null)
                {
                    continue;
                }
                al.ThumbnailUrl = urls.TryGetValue(al.ThumbnailFileId.Value, out var url) ? url : null;
            }
        }

        /// <summary>
        /// When an album is selected, get all files' information within the album
        /// </summary>
        /// <param name="albumId"></param>
        /// <returns></returns>
        public async Task<List<AlbumFileDto>> GetAlbumFilesAsync(int albumId)
        {
            var queryable = await _albumToFileRepository.GetQueryableAsync();

            var albumFiles = await queryable
                .Include(af => af.Album).ThenInclude(a => a.ThumbnailBucketFile)
                .Include(af => af.BucketFile)
                .Where(af => af.AlbumId == albumId && !af.IsDeleted)
                .OrderBy(af => af.Weight)
                .ThenByDescending(af => af.BucketFile.DeliveryDate)
                .ToListAsync();

            return ObjectMapper.Map<List<AlbumToFile>, List<AlbumFileDto>>(albumFiles);
        }
    }
}