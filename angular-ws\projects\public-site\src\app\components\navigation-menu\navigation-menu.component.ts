import { Component, OnInit, signal, computed, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { MenubarModule } from 'primeng/menubar';
import { MenuItem } from 'primeng/api';
import { ButtonModule } from 'primeng/button';
import { DropdownModule } from 'primeng/dropdown';
import { TooltipModule } from 'primeng/tooltip';
import { MenuModule } from 'primeng/menu';
import { DrawerService } from '../../services/drawer.service';
import { ReadOnlyChannelService } from '@/proxy/holy-bless/channels';
import { ChannelTreeDto } from '@/proxy/holy-bless/channels/dtos';
import { ReadOnlyCollectionService } from '@/proxy/holy-bless/collections';
import { ListStyle } from '@/proxy/holy-bless/enums';
import { I18nService, Language } from '@/services/i18n.service';
import { TranslatePipe } from '@/pipes/translate.pipe';
import { ThemeService } from '@/services/theme.service';

@Component({
  selector: 'app-navigation-menu',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MenubarModule,
    ButtonModule,
    DropdownModule,
    TooltipModule,
    MenuModule,
  ],
  templateUrl: './navigation-menu.component.html',
  styleUrls: ['./navigation-menu.component.scss'],
})
export class NavigationMenuComponent implements OnInit {
  i18nService = inject(I18nService);
  drawerService = inject(DrawerService);
  themeService = inject(ThemeService);

  menuItems: MenuItem[] = [];

  displayLanguages = computed((): MenuItem[] =>
    this.i18nService.supportedLanguages.map((lang) => ({
      label: lang.label,
      command: () => this.selectDisplayLanguage(lang),
    })),
  );

  #ReadOnlyChannelService = inject(ReadOnlyChannelService);
  #ReadOnlyCollectionService = inject(ReadOnlyCollectionService);
  router = inject(Router);

  ngOnInit() {
    this.#ReadOnlyChannelService.getChannelTree('zh-Hans').subscribe({
      next: (data) => {
        this.menuItems = this.initializeMenuItems(data);
      },
      error: (error) => {
        console.error('获取频道树数据失败:', error);
      },
    });
  }

  private initializeMenuItems(data: ChannelTreeDto[]): MenuItem[] {
    return data.map((channel) => ({
      label: channel.name,
      items: this.initializeMenuItems(channel.children),
      command: !channel.children.length
        ? this.navigateToChannel.bind(this, channel.id)
        : undefined,
    }));
  }

  navigateToChannel(channelId: number) {
    this.#ReadOnlyCollectionService.getFirstByChannelId(channelId).subscribe({
      next: (res) => {
        switch (res.listStyle) {
          case ListStyle.ImageCard:
            this.router.navigateByUrl(
              `/home/<USER>
            );
            break;
          case ListStyle.SummaryCard:
            this.router.navigateByUrl(
              `/home/<USER>
            );
            break;
          case ListStyle.ArticleTree:
            this.router.navigateByUrl(
              `/home/<USER>
            );
            break;
          case ListStyle.CollectionTree:
            this.router.navigateByUrl(
              `/home/<USER>
            );
            break;
          case ListStyle.CollectionArticleTree:
            this.router.navigateByUrl(
              `/home/<USER>
            );
            break;
        }
      },
    });
  }

  navigateToHome() {
    this.router.navigate(['/landing']);
  }

  selectDisplayLanguage(language: Language) {
    this.i18nService.setLanguage(language.code);
  }

  openSettings() {
    this.drawerService.openSettings();
    console.log('打开设置抽屉');
  }

  togglePlay() {
    this.drawerService.openPlaylist();
  }
}
