import { Injectable, signal, computed, inject } from '@angular/core';
import { DOCUMENT } from '@angular/common';

export enum AudioDeviceTypes {
  'cmn' = 'cmn',
  'yue' = 'yue',
  'eng' = 'eng',
}

export enum EyeTypes {
  'zh-Hans' = 'zh-Hans',
  'zh-Hant' = 'zh-Hant',
  'en' = 'en',
}

export interface Language {
  code: keyof typeof EyeTypes;
  label: string;
}

export interface AudioDevice {
  code: keyof typeof AudioDeviceTypes;
  label: string;
}

export interface TranslationResource {
  [key: string]: string | TranslationResource;
}

@Injectable({
  providedIn: 'root',
})
export class I18nService {
  document = inject(DOCUMENT);

  supportedLanguages: Language[] = [
    { code: 'zh-Hans', label: '简体中文' },
    { code: 'zh-Hant', label: '繁體中文' },
    { code: 'en', label: 'English' },
  ];

  supportedAudioDevices: AudioDevice[] = [
    { code: 'cmn', label: '普通话' },
    { code: 'yue', label: '粤语' },
    { code: 'eng', label: 'English' },
  ];

  language = signal<string>(this.getStoredLanguage() || 'zh-Hans');
  audioDevice = signal<string>(this.getStoredAudioDevice() || 'cmn');

  translations = signal<Record<string, TranslationResource>>({});

  currentLanguageInfo = computed(
    () =>
      this.supportedLanguages.find((lang) => lang.code === this.language()) ||
      this.supportedLanguages[0],
  );

  currentAudioDeviceInfo = computed(
    () =>
      this.supportedAudioDevices.find(
        (device) => device.code === this.audioDevice(),
      ) || this.supportedAudioDevices[0],
  );

  constructor() {
    this.loadTranslations();
  }

  setLanguage(languageCode: string) {
    this.language.set(languageCode);
    localStorage.setItem('lang', languageCode);
    this.updateDocumentLanguage();
  }

  setAudioDevice(deviceCode: string) {
    this.audioDevice.set(deviceCode);
    localStorage.setItem('audio', deviceCode);
  }

  getStoredLanguage() {
    return localStorage.getItem('lang') || EyeTypes['zh-Hans'];
  }
  getStoredAudioDevice() {
    return localStorage.getItem('audio') || AudioDeviceTypes.cmn;
  }

  // 加载翻译资源
  private async loadTranslations() {
    try {
      const [zhCN, zhTW, enUS] = await Promise.all([
        import('../../assets/i18n/zh-Hans.json'),
        import('../../assets/i18n/zh-Hant.json'),
        import('../../assets/i18n/en.json'),
      ]);

      this.translations.set({
        'zh-Hans': zhCN.default,
        'zh-Hant': zhTW.default,
        en: enUS.default,
      });
    } catch (error) {
      console.error('Failed to load translations:', error);
    }
  }

  updateDocumentLanguage() {
    this.document.documentElement.lang = this.language();
  }

  // 获取翻译文本
  translate(key: string, params?: Record<string, any>): string {
    const currentTranslations = this.translations()[this.language()];
    if (!currentTranslations) {
      return key;
    }

    const translation = this.getNestedValue(currentTranslations, key);
    if (typeof translation !== 'string') {
      return key;
    }

    if (params) {
      return Object.keys(params).reduce((text, param) => {
        return text.replace(new RegExp(`{{${param}}}`, 'g'), params[param]);
      }, translation);
    }

    return translation;
  }

  private getNestedValue(
    obj: TranslationResource,
    key: string,
  ): string | TranslationResource {
    return (
      key.split('.').reduce((current: any, k) => {
        return current && typeof current === 'object' ? current[k] : undefined;
      }, obj) || key
    );
  }
}
