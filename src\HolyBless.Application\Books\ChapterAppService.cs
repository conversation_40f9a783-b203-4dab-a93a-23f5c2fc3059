using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Repositories;
using HolyBless.Entities.Books;
using HolyBless.Permissions;
using HolyBless.Books.Dtos;

namespace HolyBless.Books;

public class ChapterAppService : ApplicationService, IChapterAppService
{
    private readonly IRepository<Chapter, int> _chapterRepository;
    private readonly IRepository<ChapterToArticle> _chapterToArticleRepository;

    public ChapterAppService(
        IRepository<Chapter, int> chapterRepository,
        IRepository<ChapterToArticle> chapterToArticleRepository)
    {
        _chapterRepository = chapterRepository;
        _chapterToArticleRepository = chapterToArticleRepository;
    }

    public async Task<ChapterDto> GetAsync(int id)
    {
        var chapter = await _chapterRepository.GetAsync(id);
        return ObjectMapper.Map<Chapter, ChapterDto>(chapter);
    }

    public async Task<PagedResultDto<ChapterDto>> GetListAsync(PagedAndSortedResultRequestDto input)
    {
        var queryable = await _chapterRepository.GetQueryableAsync();
        var query = queryable
            .OrderBy(c => c.Weight)
            .ThenBy(c => c.Title)
            .Skip(input.SkipCount)
            .Take(input.MaxResultCount);

        var chapters = await AsyncExecuter.ToListAsync(query);
        var totalCount = await AsyncExecuter.CountAsync(queryable);

        return new PagedResultDto<ChapterDto>(
            totalCount,
            ObjectMapper.Map<List<Chapter>, List<ChapterDto>>(chapters)
        );
    }

    public async Task<ChapterDto> CreateAsync(CreateUpdateChapterDto input)
    {
        var chapter = ObjectMapper.Map<CreateUpdateChapterDto, Chapter>(input);
        await _chapterRepository.InsertAsync(chapter);
        return ObjectMapper.Map<Chapter, ChapterDto>(chapter);
    }

    public async Task<ChapterDto> UpdateAsync(int id, CreateUpdateChapterDto input)
    {
        var chapter = await _chapterRepository.GetAsync(id);
        ObjectMapper.Map(input, chapter);
        await _chapterRepository.UpdateAsync(chapter);
        return ObjectMapper.Map<Chapter, ChapterDto>(chapter);
    }

    public async Task DeleteAsync(int id)
    {
        await _chapterRepository.DeleteAsync(id);
    }

    public async Task<List<ChapterDto>> GetChaptersByEBookIdAsync(int eBookId)
    {
        var queryable = await _chapterRepository.GetQueryableAsync();
        var chapters = await queryable
            .Where(c => c.EBookId == eBookId)
            .OrderBy(c => c.Weight)
            .ThenBy(c => c.Title)
            .ToListAsync();

        return ObjectMapper.Map<List<Chapter>, List<ChapterDto>>(chapters);
    }

    public async Task<List<ChapterDto>> GetChildChaptersAsync(int parentChapterId)
    {
        var queryable = await _chapterRepository.GetQueryableAsync();
        var chapters = await queryable
            .Where(c => c.ParentChapterId == parentChapterId)
            .OrderBy(c => c.Weight)
            .ThenBy(c => c.Title)
            .ToListAsync();

        return ObjectMapper.Map<List<Chapter>, List<ChapterDto>>(chapters);
    }

    public async Task<List<ChapterToArticleDto>> GetChapterArticlesAsync(int chapterId)
    {
        var queryable = await _chapterToArticleRepository.GetQueryableAsync();
        var chapterArticles = await queryable
            .Include(ca => ca.Chapter)
            .Include(ca => ca.Article)
            .Where(ca => ca.ChapterId == chapterId)
            .OrderBy(ca => ca.Weight)
            .ToListAsync();

        return ObjectMapper.Map<List<ChapterToArticle>, List<ChapterToArticleDto>>(chapterArticles);
    }

    public async Task<ChapterToArticleDto> AddArticleToChapterAsync(CreateUpdateChapterToArticleDto input)
    {
        var chapterToArticle = ObjectMapper.Map<CreateUpdateChapterToArticleDto, ChapterToArticle>(input);
        await _chapterToArticleRepository.InsertAsync(chapterToArticle);
        
        // Load the related entities for the response
        var queryable = await _chapterToArticleRepository.GetQueryableAsync();
        var result = await queryable
            .Include(ca => ca.Chapter)
            .Include(ca => ca.Article)
            .FirstAsync(ca => ca.ChapterId == input.ChapterId && ca.ArticleId == input.ArticleId);

        return ObjectMapper.Map<ChapterToArticle, ChapterToArticleDto>(result);
    }

    public async Task RemoveArticleFromChapterAsync(int chapterId, int articleId)
    {
        var chapterToArticle = await _chapterToArticleRepository.FirstOrDefaultAsync(
            ca => ca.ChapterId == chapterId && ca.ArticleId == articleId);
        
        if (chapterToArticle != null)
        {
            await _chapterToArticleRepository.DeleteAsync(chapterToArticle);
        }
    }
}
