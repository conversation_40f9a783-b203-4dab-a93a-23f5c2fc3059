using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using HolyBless.Buckets.Dtos;
using HolyBless.Entities.Buckets;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Repositories;

namespace HolyBless.Buckets
{
    public class BucketFileUrlAppService : CrudAppService<
        BucketFileUrl,
        BucketFileUrlDto,
        int,
        PagedAndSortedResultRequestDto,
        CreateUpdateBucketFileUrlDto>, IBucketFileUrlAppService
    {
        public BucketFileUrlAppService(IRepository<BucketFileUrl, int> repository)
            : base(repository)
        {
        }

        public async Task<List<BucketFileUrlDto>> GetByBucketFileIdAsync(int bucketFileId)
        {
            var urls = await Repository.GetListAsync(u => u.BucketFileId == bucketFileId);
            return ObjectMapper.Map<List<BucketFileUrl>, List<BucketFileUrlDto>>(urls);
        }

        public async Task<BucketFileUrlDto?> GetByBucketFileIdAndProviderCodeAsync(int bucketFileId, string providerCode)
        {
            var url = await Repository.FirstOrDefaultAsync(u => 
                u.BucketFileId == bucketFileId && u.ProviderCode == providerCode);
            
            return url != null ? ObjectMapper.Map<BucketFileUrl, BucketFileUrlDto>(url) : null;
        }

        public async Task<List<BucketFileUrlDto>> GetByProviderCodeAsync(string providerCode)
        {
            var urls = await Repository.GetListAsync(u => u.ProviderCode == providerCode);
            return ObjectMapper.Map<List<BucketFileUrl>, List<BucketFileUrlDto>>(urls);
        }
    }
}
