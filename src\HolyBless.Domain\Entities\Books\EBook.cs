﻿using System;
using System.Collections.Generic;
using HolyBless.Books;
using HolyBless.Entities.Buckets;
using HolyBless.Entities.Channels;

namespace HolyBless.Entities.Books
{
    public class EBook : LanguageAuditedAggregateRoot<int>
    {
        public string Title { get; set; } = "";
        public string? Description { get; set; }
        public int Weight { get; set; } = 0;
        public int? ChannelId { get; set; }
        public Channel? Channel { get; set; }
        public int? ThumbnailFileId { get; set; }  //Thumbnail Image FileName
        public BucketFile? ThumbnailBucketFile { get; set; }
        public BookType Type { get; set; }

        public DateTime DeliveryDate { get; set; }

        public int Views { get; set; }
        public int Likes { get; set; }

        // Navigation properties
        public ICollection<Chapter> Chapters { get; set; } = [];
    }
}