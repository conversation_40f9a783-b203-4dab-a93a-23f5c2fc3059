import { Component, computed, inject, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { OrderListModule } from 'primeng/orderlist';
import { TreeModule } from 'primeng/tree';
import { TreeNode } from 'primeng/api';
import { ReadOnlyArticleService } from '@/proxy/holy-bless/articles';
import { ActivatedRoute } from '@angular/router';
import { ReadOnlyCollectionService } from '@/proxy/holy-bless/collections';
import {
  ArticleAggregateResult,
  ArticleFileAggregateResult,
} from '@/proxy/holy-bless/results';
import { MediaType } from '@/proxy/holy-bless/enums';
import { GalleriaModule } from 'primeng/galleria';

@Component({
  selector: 'app-artical-tree',
  standalone: true,
  imports: [CommonModule, TreeModule, GalleriaModule],
  templateUrl: './article-tree.html',
  styleUrls: ['./article-tree.scss'],
})
export class ArticleTreeComponent {
  #ReadOnlyCollectionService = inject(ReadOnlyCollectionService);
  #ReadOnlyArticleService = inject(ReadOnlyArticleService);
  #route = inject(ActivatedRoute);

  collectionId: number | null = null;
  files: TreeNode[] = [];

  articleDetail = signal<ArticleAggregateResult | null>(null);
  articleFiles = computed(() => this.articleDetail()?.articleFiles || []);
  primaryArticleFiles = computed(() =>
    this.articleFiles().filter((file) => file.isPrimary === true),
  );
  imageArticleFiles = computed(() =>
    this.articleFiles().filter((file) => file.mediaType === MediaType.Image),
  );
  notImageArticleFiles = computed(() =>
    this.articleFiles().filter((file) => file.mediaType !== MediaType.Image),
  );

  constructor() {}

  selectedFile!: TreeNode;
  ngOnInit() {
    this.#route.queryParams.subscribe((params) => {
      this.collectionId = params['collectionId'];
      this.loadCollectionSummary();
    });
  }

  loadCollectionSummary() {
    this.#ReadOnlyCollectionService
      .getCollectionArticleTitles(this.collectionId!)
      .subscribe({
        next: (data) => {
          this.files = data.map((item) => this.buildTreeNode(item));
        },
        error: (error) => {
          console.error('获取文章树数据失败:', error);
        },
      });
  }

  buildTreeNode(node: any): TreeNode {
    return {
      key: node.id,
      label: node.name || node.title,
      data: node,
      children: node.articles
        ? node.articles.map((article: any) => this.buildTreeNode(article))
        : [],
    };
  }

  loadArticalDetail(node: TreeNode) {
    if (!node.key) return;
    this.#ReadOnlyArticleService
      .getArticleAggregate(Number(node.key))
      .subscribe({
        next: (data) => {
          console.log('文章详情:', data);
          this.articleDetail.set(data);
        },
        error: (error) => {
          console.error('获取文章详情失败:', error);
        },
      });
  }
}
