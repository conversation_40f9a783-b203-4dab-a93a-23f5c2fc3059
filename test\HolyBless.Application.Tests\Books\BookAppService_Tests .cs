﻿using System;
using System.Linq;
using System.Threading.Tasks;
using HolyBless.Books.Dtos;
using Shouldly;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Modularity;
using Volo.Abp.Validation;
using Xunit;

namespace HolyBless.Books;

public abstract class BookAppService_Tests<TStartupModule> : HolyBlessApplicationTestBase<TStartupModule>
    where TStartupModule : IAbpModule
{
    private readonly IEBookAppService _bookAppService;

    protected BookAppService_Tests()
    {
        _bookAppService = GetRequiredService<IEBookAppService>();
    }

    [Fact]
    public async Task Should_Get_List_Of_Books()
    {
        //Act
        var result = await _bookAppService.GetListAsync(
            new PagedAndSortedResultRequestDto()
        );

        //Assert
        result.TotalCount.ShouldBeGreaterThan(0);
        result.Items.ShouldContain(b => b.Title == "1984");
    }

    [Fact]
    public async Task Should_Create_A_Valid_Book()
    {
        //Act
        var result = await _bookAppService.CreateAsync(
            new CreateUpdateEBookDto
            {
                Title = "New test book 42",
                Views = 10,
                DeliveryDate = DateTime.Now,
                Type = BookType.LingYuJue
            }
        );

        //Assert
        result.Id.ShouldNotBe(0);
        result.Title.ShouldBe("New test book 42");
    }

    [Fact]
    public async Task Should_Not_Create_A_Book_Without_Title()
    {
        var exception = await Assert.ThrowsAsync<AbpValidationException>(async () =>
        {
            await _bookAppService.CreateAsync(
                new CreateUpdateEBookDto
                {
                    Title = "",
                    Views = 10,
                    DeliveryDate = DateTime.Now,
                    Type = BookType.LingYuJue
                }
            );
        });

        exception.ValidationErrors
            .ShouldContain(err => err.MemberNames.Any(mem => mem == "Title"));
    }
}