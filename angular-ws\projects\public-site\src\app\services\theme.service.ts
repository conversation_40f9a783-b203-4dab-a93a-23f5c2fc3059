import { Injectable, signal, computed, inject } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class ThemeService {
  constructor() {
    this.setTheme(this.getStoredTheme() || 'light');
  }
  setTheme(theme: 'light' | 'dark') {
    document.documentElement.classList.remove('app-light', 'app-dark');
    document.documentElement.classList.add(`app-${theme}`);
    localStorage.setItem('theme', theme);
  }

  getStoredTheme(): 'light' | 'dark' {
    const theme = localStorage.getItem('theme');
    return theme === 'dark' ? 'dark' : 'light';
  }

  toggleTheme() {
    const currentTheme = this.getStoredTheme();
    const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
    this.setTheme(newTheme);
  }
}
