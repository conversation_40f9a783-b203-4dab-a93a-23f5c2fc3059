﻿using HolyBless.Books.Dtos;
using System;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace HolyBless.Books;

public interface IEBookAppService : IReadOnlyEBookAppService,
    ICrudAppService< //Defines CRUD methods
        EBookDto, //Used to show books
        int, //Primary key of the book entity
        PagedAndSortedResultRequestDto, //Used for paging/sorting
        CreateUpdateEBookDto> //Used to create/update a book
{

}