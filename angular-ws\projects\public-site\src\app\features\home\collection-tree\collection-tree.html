<div class="flex flex-1">
  <p-tree
    [value]="files"
    styleClass="w-[20rem] h-full"
    selectionMode="single"
    [(selection)]="selectedFile"
    [virtualScroll]="true"
    virtualScrollItemSize="36"
    [filter]="true"
    (onNodeSelect)="loadArticleSummary($event.node)"
  />
  <div
    class="articaldetail-container prose max-w-none p-6 flex-1 overflow-y-auto"
    style="height: calc(100vh - 5rem)"
  >
    <p-accordion value="0">
      @for (item of items; track $index) {
      <p-accordion-panel [value]="item.id">
        <p-accordion-header>
          <p class="flex-1 flex justify-between items-center px-5">
            <a name="_Toc{{ item.id }}">{{ item.title }}</a>
            <!-- <button
              class="flex items-center gap-2 border bg-white px-2 py-1 rounded-md text-sm text-black hover:bg-gray-100"
              (click)="onPlayClick($event, item)"
            >
              <i class="pi pi-play"></i>
              播放
            </button> -->
            <p-button
              icon="pi pi-play"
              label="播放"
              variant="outlined"
              (click)="onPlayClick(item)"
            />
          </p>
        </p-accordion-header>
        <p-accordion-content>
          <p class="m-0" [innerHTML]="item.content"></p>
        </p-accordion-content>
      </p-accordion-panel>
      }
    </p-accordion>
  </div>
  <div class="p-6 w-[20rem] border-l-2">
    <div>
      <div class="flex flex-col max-h-[80vh] overflow-y-auto">
        <h3 class="text-lg font-semibold mb-4">目录</h3>

        <!-- 如果没有目录项，显示提示 -->
        <div *ngIf="items.length === 0" class="text-sm italic">
          暂无目录
        </div>

        <!-- 动态生成的目录项 -->
        <a
          *ngFor="let item of items;"
          class="mt-2 cursor-pointer hover:text-primary-600 underline"
          (click)="onTocItemClick(item)"
        >
          {{ item.title }}
        </a>
      </div>
    </div>
  </div>
</div>
