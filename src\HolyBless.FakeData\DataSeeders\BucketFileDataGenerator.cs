using Bogus;
using HolyBless.Entities.Buckets;
using HolyBless.Enums;
using System;
using System.Collections.Generic;

namespace HolyBless.FakeData.DataSeeders
{
    public static class BucketFileDataGenerator
    {
        private static readonly Random _random = new Random();
        private static readonly MediaType[] _mediaTypes = (MediaType[])Enum.GetValues(typeof(MediaType));
        private static readonly ContentCategory[] _contentCategories = (ContentCategory[])Enum.GetValues(typeof(ContentCategory));

        private static readonly string[] EnglishTitles =
        [
            "Spiritual Awakening Journey", "Light of Wisdom", "Path of Practice Insights", "Power of Love and Compassion",
            "True Meaning of Life", "Dream Revelations", "Miracles of Heaven's Gate", "Power of Peace and Blessings",
            "Secrets of Spiritual Growth", "Practice Q&A", "Philosophy of Life and Death", "Daily Reflections",
            "Stories of Turning Back", "Grace of Teachers", "Sharing Practice Experiences", "Wisdom from the Past"
        ];

        private static readonly string[] HansTitles =
        [
            "心灵觉醒的旅程", "智慧之光", "修行路上的感悟", "爱与慈悲的力量",
            "生命的真谛", "梦境中的启示", "天门开启的奇迹", "平安祝福的力量",
            "心灵成长的秘诀", "修行答疑", "生死哲学", "每日反思",
            "回头是岸的故事", "师恩如山", "修行心得分享", "温故知新"
        ];

        private static readonly string[] HantTitles =
        [
            "心靈覺醒的旅程", "智慧之光", "修行路上的感悟", "愛與慈悲的力量",
            "生命的真諦", "夢境中的啟示", "天門開啟的奇蹟", "平安祝福的力量",
            "心靈成長的祕訣", "修行答疑", "生死哲學", "每日反思",
            "回頭是岸的故事", "師恩如山", "修行心得分享", "溫故知新"
        ];

        public enum BucketFileLanguage { English, Hans, Hant }

        public static IEnumerable<BucketFile> Generate(int count, BucketFileLanguage language)
        {
            var currentId = 1;
            var curId = 1;
            string[] titles;
            string langCode;
            string locale;
            int idOffset;

            switch (language)
            {
                case BucketFileLanguage.English:
                    titles = EnglishTitles;
                    langCode = LangCode.English;
                    locale = "en";
                    idOffset = 2000;
                    break;
                case BucketFileLanguage.Hans:
                    titles = HansTitles;
                    langCode = LangCode.SimplifiedChinese;
                    locale = "zh_CN";
                    idOffset = 1;
                    break;
                case BucketFileLanguage.Hant:
                    titles = HantTitles;
                    langCode = LangCode.TraditionalChinese;
                    locale = "zh_TW";
                    idOffset = 1000;
                    break;
                default:
                    throw new ArgumentException("Invalid language");
            }

            var faker = new Faker<BucketFile>(locale)
                .RuleFor(f => f.Id, f => idOffset + currentId++)
                .RuleFor(f => f.LanguageCode, f => langCode)
                .RuleFor(f => f.FileName, f => f.System.FileName())
                .RuleFor(f => f.Title, f => f.PickRandom(titles))
                .RuleFor(f => f.RelativePathInBucket, "")
                .RuleFor(f => f.MediaType, f => _mediaTypes[_random.Next(_mediaTypes.Length)])
                .RuleFor(f => f.ContentCategory, f => _contentCategories[_random.Next(_contentCategories.Length)])
                .RuleFor(f => f.DeliveryDate, f => SharedDeliveryDates.GetDeliveryDate(curId++))
                .RuleFor(f => f.Views, f => f.Random.Int(0, 1000))
                .RuleFor(f => f.YoutubeId, f => "")
                .RuleFor(f => f.FolderToBucketFiles, f => new List<global::HolyBless.Entities.VirtualFolders.FolderToFile>())
                .RuleFor(f => f.CollectionToFiles, f => new List<global::HolyBless.Entities.Collections.CollectionToFile>());

            return faker.Generate(count);
        }
    }
}
