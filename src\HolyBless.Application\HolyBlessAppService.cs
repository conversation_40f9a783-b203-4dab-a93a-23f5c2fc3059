﻿using System.Collections.Generic;
using System.Linq;
using HolyBless.Entities.Buckets;
using HolyBless.Enums;
using HolyBless.Localization;
using Volo.Abp.Application.Services;

namespace HolyBless;

/* Inherit your application services from this class.
 */

public abstract class HolyBlessAppService : ApplicationService
{
    protected HolyBlessAppService()
    {
        LocalizationResource = typeof(HolyBlessResource);
    }

    protected virtual string ChooseUrl(ICollection<BucketFileUrl>? urls, string providerCode)
    {
        if (urls == null || urls.Count == 0)
        {
            return string.Empty;
        }
        if (urls.Count == 1)
        {
            return urls.First().ComputeUrl;
        }
        var matchedUrl = urls.FirstOrDefault(x => x.ProviderCode == providerCode);
        if (matchedUrl != null)
        {
            return matchedUrl.ComputeUrl;
        }
        matchedUrl = urls.FirstOrDefault(x => x.ProviderCode == ProviderCodeConstants.CloudFlare);
        return matchedUrl?.ComputeUrl ?? string.Empty;
    }
}