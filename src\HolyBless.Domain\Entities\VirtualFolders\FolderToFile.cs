﻿using HolyBless.Entities.Buckets;
using Volo.Abp;
using Volo.Abp.Domain.Entities;

namespace HolyBless.Entities.VirtualFolders
{
    public class FolderToFile : Entity, ISoftDelete
    {
        public int FolderId { get; set; }
        public VirtualDiskFolder Folder { get; set; } = default!;
        public string? Title { get; set; }  //Default to File Name, but can use different name for language
        public int BucketFileId { get; set; }
        public BucketFile BucketFile { get; set; } = default!;
        public bool IsDeleted { get; set; }

        public override object?[] GetKeys()
        {
            return [FolderId, BucketFileId];
        }
    }
}