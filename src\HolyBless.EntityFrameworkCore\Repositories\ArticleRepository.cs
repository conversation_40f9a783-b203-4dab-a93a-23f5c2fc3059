using System;
using System.Linq;
using System.Collections.Generic;
using System.Threading.Tasks;
using HolyBless.Entities.Articles;
using HolyBless.EntityFrameworkCore;
using HolyBless.Results;
using Microsoft.EntityFrameworkCore;
using Volo.Abp.Domain.Repositories.EntityFrameworkCore;
using Volo.Abp.EntityFrameworkCore;
using HolyBless.Domain.Interfaces;
using HolyBless.Enums;

namespace HolyBless.Repositories
{
    public class ArticleRepository : EfCoreRepository<HolyBlessDbContext, Article, int>, IArticleRepository
    {
        public ArticleRepository(IDbContextProvider<HolyBlessDbContext> dbContextProvider)
            : base(dbContextProvider)
        {
        }

        public async Task<ArticleAggregateResult?> GetArticleAggregateAsync(int articleId, string providerCode)
        {
            var results = await GetArticleAggregatesAsync(new List<int> { articleId }, providerCode);
            return results.FirstOrDefault();
        }

        public async Task<List<ArticleAggregateResult>> GetArticleAggregatesAsync(List<int> articleIds, string providerCode)
        {
            if (!articleIds.Any())
            {
                return new List<ArticleAggregateResult>();
            }

            var dbContext = await GetDbContextAsync();

            var articles = await dbContext.Articles
                .Where(a => articleIds.Contains(a.Id))
                .Include(a => a.ArticleFiles)
                    .ThenInclude(af => af.BucketFile)
                        .ThenInclude(bf => bf.BucketFileUrls)
                .Include(a => a.ThumbnailBucketFile)
                    .ThenInclude(bf => bf.BucketFileUrls)
                .OrderByDescending(a => a.DeliveryDate)
                .ToListAsync();

            var results = articles.Select(article => new ArticleAggregateResult
            {
                Id = article.Id,
                Title = article.Title,
                Description = article.Description,
                Keywords = article.Keywords,
                Views = article.Views,
                Likes = article.Likes,
                DeliveryDate = article.DeliveryDate,
                ArticleContentCategory = article.ArticleContentCategory,
                Status = article.Status,
                Content = article.Content,
                Memo = article.Memo,
                CreationTime = article.CreationTime,
                LastModificationTime = article.LastModificationTime,
                ThumbnailUrl = article.ThumbnailBucketFile?.BucketFileUrls
                    ?.FirstOrDefault(u => u.ProviderCode == providerCode || u.ProviderCode == ProviderCodeConstants.CloudFlare)?.ComputeUrl,
                ArticleFiles = article.ArticleFiles?.Select(af => new ArticleFileAggregateResult
                {
                    Id = af.Id,
                    FileId = af.FileId,
                    Title = af.Title,
                    Description = af.Description,
                    IsPrimary = af.IsPrimary,
                    FileName = af.BucketFile.FileName,
                    MediaType = af.BucketFile.MediaType,
                    ContentCategory = af.BucketFile.ContentCategory,
                    FileDeliveryDate = af.BucketFile.DeliveryDate,
                    FileViews = af.BucketFile.Views,
                    YoutubeId = af.BucketFile.YoutubeId,
                    FileUrl = af.BucketFile.BucketFileUrls
                        ?.FirstOrDefault(u => u.ProviderCode == providerCode || u.ProviderCode == ProviderCodeConstants.CloudFlare)?.ComputeUrl ?? ""
                }).ToList() ?? new List<ArticleFileAggregateResult>()
            }).ToList();

            return results;
        }
    }
}