using System;
using Volo.Abp.Application.Dtos;

namespace HolyBless.Books.Dtos;

public class ChapterDto : AuditedEntityDto<int>
{
    public int EBookId { get; set; }
    public int? ParentChapterId { get; set; }
    public string Title { get; set; } = "";
    public string? Description { get; set; }
    public int Weight { get; set; } = 0;
    public int Views { get; set; } = 0;
    public int Likes { get; set; } = 0;
    public string? Content { get; set; }
    public int? ContentAudioFileId { get; set; }

}
