using System.ComponentModel.DataAnnotations;

namespace HolyBless.Books.Dtos;

public class CreateUpdateChapterDto
{
    [Required]
    public int EBookId { get; set; }

    public int? ParentChapterId { get; set; }

    [Required]
    [StringLength(200)]
    public string Title { get; set; } = string.Empty;

    [StringLength(500)]
    public string? Description { get; set; }

    public int Weight { get; set; } = 0;

    public int Views { get; set; } = 0;

    public int Likes { get; set; } = 0;

    public string? Content { get; set; }
    public int? ContentAudioFileId { get; set; }

}
