﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace HolyBless.Migrations
{
    /// <inheritdoc />
    public partial class chaptertable : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_EBooks_Articles_PrefaceArticleId",
                table: "EBooks");

            migrationBuilder.DropForeignKey(
                name: "FK_EBooks_BucketFiles_ThumbnailBucketFileId",
                table: "EBooks");

            migrationBuilder.DropForeignKey(
                name: "FK_VirtualDiskFolders_Articles_ArticleId",
                table: "VirtualDiskFolders");

            migrationBuilder.DropTable(
                name: "Series");

            migrationBuilder.DropIndex(
                name: "IX_VirtualDiskFolders_ArticleId",
                table: "VirtualDiskFolders");

            migrationBuilder.DropIndex(
                name: "IX_EBooks_PrefaceArticleId",
                table: "EBooks");

            migrationBuilder.DropColumn(
                name: "ArticleId",
                table: "VirtualDiskFolders");

            migrationBuilder.DropColumn(
                name: "Size",
                table: "VirtualDiskFolders");

            migrationBuilder.DropColumn(
                name: "ContentCode",
                table: "EBooks");

            migrationBuilder.DropColumn(
                name: "Preface",
                table: "EBooks");

            migrationBuilder.DropColumn(
                name: "PrefaceArticleId",
                table: "EBooks");

            migrationBuilder.RenameColumn(
                name: "ThumbnailBucketFileId",
                table: "EBooks",
                newName: "ChannelId");

            migrationBuilder.RenameColumn(
                name: "PublishDate",
                table: "EBooks",
                newName: "DeliveryDate");

            migrationBuilder.RenameIndex(
                name: "IX_EBooks_ThumbnailBucketFileId",
                table: "EBooks",
                newName: "IX_EBooks_ChannelId");

            migrationBuilder.AddColumn<string>(
                name: "ContentCode",
                table: "VirtualDiskFolders",
                type: "character varying(50)",
                unicode: false,
                maxLength: 50,
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "Weight",
                table: "VirtualDiskFolders",
                type: "integer",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<string>(
                name: "Description",
                table: "EBooks",
                type: "character varying(256)",
                maxLength: 256,
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "Weight",
                table: "EBooks",
                type: "integer",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "VirtualDiskFolderId",
                table: "Articles",
                type: "integer",
                nullable: true);

            migrationBuilder.CreateTable(
                name: "Chapters",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    EBookId = table.Column<int>(type: "integer", nullable: false),
                    ParentChapterId = table.Column<int>(type: "integer", nullable: true),
                    Title = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Description = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    Weight = table.Column<int>(type: "integer", nullable: false),
                    Views = table.Column<int>(type: "integer", nullable: false),
                    Likes = table.Column<int>(type: "integer", nullable: false),
                    Content = table.Column<string>(type: "text", nullable: true),
                    ContentAudioFileId = table.Column<int>(type: "integer", nullable: true),
                    ExtraProperties = table.Column<string>(type: "text", nullable: false),
                    ConcurrencyStamp = table.Column<string>(type: "character varying(40)", maxLength: 40, nullable: false),
                    CreationTime = table.Column<DateTime>(type: "timestamp without time zone", nullable: false),
                    CreatorId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModificationTime = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    LastModifierId = table.Column<Guid>(type: "uuid", nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    DeleterId = table.Column<Guid>(type: "uuid", nullable: true),
                    DeletionTime = table.Column<DateTime>(type: "timestamp without time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Chapters", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Chapters_BucketFiles_ContentAudioFileId",
                        column: x => x.ContentAudioFileId,
                        principalTable: "BucketFiles",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.SetNull);
                    table.ForeignKey(
                        name: "FK_Chapters_Chapters_ParentChapterId",
                        column: x => x.ParentChapterId,
                        principalTable: "Chapters",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_Chapters_EBooks_EBookId",
                        column: x => x.EBookId,
                        principalTable: "EBooks",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "ChapterToArticles",
                columns: table => new
                {
                    ChapterId = table.Column<int>(type: "integer", nullable: false),
                    ArticleId = table.Column<int>(type: "integer", nullable: false),
                    Weight = table.Column<int>(type: "integer", nullable: false),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ChapterToArticles", x => new { x.ChapterId, x.ArticleId });
                    table.ForeignKey(
                        name: "FK_ChapterToArticles_Articles_ArticleId",
                        column: x => x.ArticleId,
                        principalTable: "Articles",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_ChapterToArticles_Chapters_ChapterId",
                        column: x => x.ChapterId,
                        principalTable: "Chapters",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_EBooks_ThumbnailFileId",
                table: "EBooks",
                column: "ThumbnailFileId");

            migrationBuilder.CreateIndex(
                name: "IX_Articles_VirtualDiskFolderId",
                table: "Articles",
                column: "VirtualDiskFolderId");

            migrationBuilder.CreateIndex(
                name: "IX_Chapters_ContentAudioFileId",
                table: "Chapters",
                column: "ContentAudioFileId");

            migrationBuilder.CreateIndex(
                name: "IX_Chapters_EBookId",
                table: "Chapters",
                column: "EBookId");

            migrationBuilder.CreateIndex(
                name: "IX_Chapters_ParentChapterId",
                table: "Chapters",
                column: "ParentChapterId");

            migrationBuilder.CreateIndex(
                name: "IX_ChapterToArticles_ArticleId_Weight",
                table: "ChapterToArticles",
                columns: new[] { "ArticleId", "Weight" });

            migrationBuilder.CreateIndex(
                name: "IX_ChapterToArticles_ChapterId_Weight",
                table: "ChapterToArticles",
                columns: new[] { "ChapterId", "Weight" });

            migrationBuilder.AddForeignKey(
                name: "FK_Articles_VirtualDiskFolders_VirtualDiskFolderId",
                table: "Articles",
                column: "VirtualDiskFolderId",
                principalTable: "VirtualDiskFolders",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_EBooks_BucketFiles_ThumbnailFileId",
                table: "EBooks",
                column: "ThumbnailFileId",
                principalTable: "BucketFiles",
                principalColumn: "Id",
                onDelete: ReferentialAction.SetNull);

            migrationBuilder.AddForeignKey(
                name: "FK_EBooks_Channels_ChannelId",
                table: "EBooks",
                column: "ChannelId",
                principalTable: "Channels",
                principalColumn: "Id",
                onDelete: ReferentialAction.SetNull);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Articles_VirtualDiskFolders_VirtualDiskFolderId",
                table: "Articles");

            migrationBuilder.DropForeignKey(
                name: "FK_EBooks_BucketFiles_ThumbnailFileId",
                table: "EBooks");

            migrationBuilder.DropForeignKey(
                name: "FK_EBooks_Channels_ChannelId",
                table: "EBooks");

            migrationBuilder.DropTable(
                name: "ChapterToArticles");

            migrationBuilder.DropTable(
                name: "Chapters");

            migrationBuilder.DropIndex(
                name: "IX_EBooks_ThumbnailFileId",
                table: "EBooks");

            migrationBuilder.DropIndex(
                name: "IX_Articles_VirtualDiskFolderId",
                table: "Articles");

            migrationBuilder.DropColumn(
                name: "ContentCode",
                table: "VirtualDiskFolders");

            migrationBuilder.DropColumn(
                name: "Weight",
                table: "VirtualDiskFolders");

            migrationBuilder.DropColumn(
                name: "Description",
                table: "EBooks");

            migrationBuilder.DropColumn(
                name: "Weight",
                table: "EBooks");

            migrationBuilder.DropColumn(
                name: "VirtualDiskFolderId",
                table: "Articles");

            migrationBuilder.RenameColumn(
                name: "DeliveryDate",
                table: "EBooks",
                newName: "PublishDate");

            migrationBuilder.RenameColumn(
                name: "ChannelId",
                table: "EBooks",
                newName: "ThumbnailBucketFileId");

            migrationBuilder.RenameIndex(
                name: "IX_EBooks_ChannelId",
                table: "EBooks",
                newName: "IX_EBooks_ThumbnailBucketFileId");

            migrationBuilder.AddColumn<int>(
                name: "ArticleId",
                table: "VirtualDiskFolders",
                type: "integer",
                nullable: true);

            migrationBuilder.AddColumn<long>(
                name: "Size",
                table: "VirtualDiskFolders",
                type: "bigint",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ContentCode",
                table: "EBooks",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Preface",
                table: "EBooks",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "PrefaceArticleId",
                table: "EBooks",
                type: "integer",
                nullable: true);

            migrationBuilder.CreateTable(
                name: "Series",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    ChannelId = table.Column<int>(type: "integer", nullable: true),
                    ThumbnailFileId = table.Column<int>(type: "integer", nullable: true),
                    ConcurrencyStamp = table.Column<string>(type: "character varying(40)", maxLength: 40, nullable: false),
                    ContentCode = table.Column<string>(type: "text", nullable: true),
                    CreationTime = table.Column<DateTime>(type: "timestamp without time zone", nullable: false),
                    CreatorId = table.Column<Guid>(type: "uuid", nullable: true),
                    DeleterId = table.Column<Guid>(type: "uuid", nullable: true),
                    DeletionTime = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    Description = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ExtraProperties = table.Column<string>(type: "text", nullable: false),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    LanguageCode = table.Column<string>(type: "character varying(10)", unicode: false, maxLength: 10, nullable: true),
                    LastModificationTime = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    LastModifierId = table.Column<Guid>(type: "uuid", nullable: true),
                    Title = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Series", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Series_BucketFiles_ThumbnailFileId",
                        column: x => x.ThumbnailFileId,
                        principalTable: "BucketFiles",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.SetNull);
                    table.ForeignKey(
                        name: "FK_Series_Channels_ChannelId",
                        column: x => x.ChannelId,
                        principalTable: "Channels",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.SetNull);
                });

            migrationBuilder.CreateIndex(
                name: "IX_VirtualDiskFolders_ArticleId",
                table: "VirtualDiskFolders",
                column: "ArticleId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_EBooks_PrefaceArticleId",
                table: "EBooks",
                column: "PrefaceArticleId");

            migrationBuilder.CreateIndex(
                name: "IX_Series_ChannelId",
                table: "Series",
                column: "ChannelId");

            migrationBuilder.CreateIndex(
                name: "IX_Series_ThumbnailFileId",
                table: "Series",
                column: "ThumbnailFileId");

            migrationBuilder.AddForeignKey(
                name: "FK_EBooks_Articles_PrefaceArticleId",
                table: "EBooks",
                column: "PrefaceArticleId",
                principalTable: "Articles",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_EBooks_BucketFiles_ThumbnailBucketFileId",
                table: "EBooks",
                column: "ThumbnailBucketFileId",
                principalTable: "BucketFiles",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_VirtualDiskFolders_Articles_ArticleId",
                table: "VirtualDiskFolders",
                column: "ArticleId",
                principalTable: "Articles",
                principalColumn: "Id");
        }
    }
}
