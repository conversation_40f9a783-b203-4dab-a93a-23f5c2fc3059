<div class="flex flex-1">
  <div class="articaldetail-container prose max-w-none p-6 flex-1">
    <h1 class="text-3xl font-bold mb-4">{{articleDetail()?.title}}</h1>
    <p class="text-sm mb-4 flex items-center gap-2">
      @if (articleDetail()) {
      <i class="pi pi-clock"></i>
      {{ articleDetail()?.deliveryDate | date: 'yy-MM-dd HH:mm:ss' }} }
    </p>
    <div class="mb-4" [innerHTML]="articleDetail()?.content"></div>
  </div>
  <div class="p-6">
    <div>
      <p-galleria
        [value]="imageArticleFiles()"
        indicatorsPosition="right"
        [showIndicators]="true"
        [showThumbnails]="false"
        [showIndicatorsOnItem]="true"
        [containerStyle]="{'width': '100%','margin-top': '2em'}"
      >
        <ng-template pTemplate="item" let-item>
          <img [src]="item.fileUrl" class="w-72 h-96 rounded-lg shadow-lg" />
        </ng-template>
      </p-galleria>
    </div>
    <div>
      <div class="mt-6">
        @if (primaryArticleFiles().length > 0) {
        <h3>媒体播放</h3>
        @for (item of primaryArticleFiles(); track $index) {
        <p class="flex justify-between items-center mt-3">
          <span>{{ item.fileName }}</span> <i class="pi pi-play-circle"></i>
        </p>
        } }
      </div>
      <div class="mt-6">
        @if (notImageArticleFiles().length > 0) {
        <h3>附件下载</h3>
        @for (item of notImageArticleFiles(); track $index) {
        <p class="flex justify-between items-center mt-3">
          <span>{{ item.fileName }}</span> <i class="pi pi-download"></i>
        </p>
        } }
      </div>
    </div>
  </div>
</div>
