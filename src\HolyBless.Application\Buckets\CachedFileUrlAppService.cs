using System;
using System.Collections.Concurrent;
using System.Linq;
using System.Collections.Generic;
using System.Threading.Tasks;
using HolyBless.Entities.Buckets;
using Microsoft.Extensions.Caching.Memory;
using Volo.Abp;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Repositories;

namespace HolyBless.Buckets
{
    [RemoteService(false)]
    public class CachedFileUrlAppService : HolyBlessAppService, ICachedFileUrlAppService
    {
        protected readonly IRepository<BucketFileUrl, int> _bucketFileUrlRepository;
        protected readonly IMemoryCache _memoryCache;
        protected static readonly string KeyFileUrl = "fileurl_cache";
        protected static readonly string KeyFileUrlTracker = "fileurl_cache_tracker";

        protected readonly MemoryCacheEntryOptions _memoryCacheEntryOptions = new()
        {
            AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(20),
            SlidingExpiration = TimeSpan.FromMinutes(10),
            Priority = CacheItemPriority.High
        };

        public CachedFileUrlAppService(
            IRepository<BucketFileUrl, int> bucketFileUrlRepository,
            IMemoryCache memoryCache)
        {
            _bucketFileUrlRepository = bucketFileUrlRepository;
            _memoryCache = memoryCache;
        }

        /// <summary>
        /// Gets the ComputeUrl for a list of file IDs from cache or database.
        /// If any fileId is not in cache, queries DB for all, sets cache for all, and returns URLs for each fileId.
        /// </summary>
        /// <param name="fileIds">List of BucketFile IDs</param>
        /// <param name="providerCode">The provider code (e.g., CloudFlare)</param>
        /// <returns>Dictionary mapping fileId to ComputeUrl (empty string if not found)</returns>
        public async Task<Dictionary<int, string>> GetCachedComputeUrlAsync(List<int?> fileIds, string providerCode)
        {
            var withFileIds = fileIds.Where(id => id.HasValue).Select(id => id.Value).ToList();
            var result = new Dictionary<int, string>();
            var cacheKeys = withFileIds.Select(id => $"{KeyFileUrl}_{id}_{providerCode}").ToList();
            var missingIds = new List<int>();

            // Check cache for each fileId
            for (int i = 0; i < withFileIds.Count; i++)
            {
                if (!_memoryCache.TryGetValue(cacheKeys[i], out string? computeUrl))
                {
                    missingIds.Add(withFileIds[i]);
                }
                else
                {
                    result[withFileIds[i]] = computeUrl ?? string.Empty;
                }
            }

            // If any are missing, query DB for all fileIds
            if (missingIds.Count > 0)
            {
                var bucketFileUrls = await _bucketFileUrlRepository.GetListAsync(u => missingIds.Contains(u.BucketFileId));
                foreach (var id in missingIds)
                {
                    var urlsForId = bucketFileUrls.Where(u => u.BucketFileId == id).ToList();
                    var url = ChooseUrl(urlsForId, providerCode);
                    result[id] = url ?? string.Empty;
                    // Set cache for each
                    var cacheKey = $"{KeyFileUrl}_{id}_{providerCode}";
                    _memoryCache.Set(cacheKey, url, _memoryCacheEntryOptions);
                    AddCacheKeyToTracker(cacheKey);
                }
            }

            return result;
        }

        /// <summary>
        /// Gets the ComputeUrl for a file from cache or database.
        /// Cache key format: "fileurl_cache_{fileId}_{providerCode}"
        /// </summary>
        /// <param name="fileId">The BucketFile ID</param>
        /// <param name="providerCode">The provider code (e.g., CloudFlare)</param>
        /// <returns>The ComputeUrl or empty string if not found</returns>
        public async Task<string> GetCachedComputeUrlAsync(int? fileId, string providerCode)
        {
            if (fileId == null) return string.Empty;
            var cacheKey = $"{KeyFileUrl}_{fileId}_{providerCode}";

            if (!_memoryCache.TryGetValue(cacheKey, out string? computeUrl))
            {
                // Fetch from database if not in cache
                var bucketFileUrls = await _bucketFileUrlRepository.GetListAsync(u =>
                    u.BucketFileId == fileId);

                computeUrl = ChooseUrl(bucketFileUrls, providerCode);

                // Cache the result (even if empty to avoid repeated database calls)
                _memoryCache.Set(cacheKey, computeUrl, _memoryCacheEntryOptions);

                // Track this cache key for bulk clearing
                AddCacheKeyToTracker(cacheKey);
            }

            return computeUrl ?? string.Empty;
        }

        /// <summary>
        /// Adds a cache key to the tracker for bulk clearing operations.
        /// </summary>
        /// <param name="cacheKey">The cache key to track</param>
        private void AddCacheKeyToTracker(string cacheKey)
        {
            if (!_memoryCache.TryGetValue(KeyFileUrlTracker, out ConcurrentBag<string>? trackedKeys))
            {
                trackedKeys = new ConcurrentBag<string>();
                _memoryCache.Set(KeyFileUrlTracker, trackedKeys, new MemoryCacheEntryOptions
                {
                    Priority = CacheItemPriority.NeverRemove
                });
            }

            trackedKeys.Add(cacheKey);
        }

        /// <summary>
        /// Clears all cached file URL entries from memory cache.
        /// This method removes all tracked cache entries and clears the tracker.
        /// </summary>
        public void ClearAllFileUrlCaches()
        {
            if (_memoryCache.TryGetValue(KeyFileUrlTracker, out ConcurrentBag<string>? trackedKeys))
            {
                foreach (var key in trackedKeys)
                {
                    _memoryCache.Remove(key);
                }

                // Clear the tracker itself
                _memoryCache.Remove(KeyFileUrlTracker);
            }
        }

        /// <summary>
        /// Clears a specific cached file URL entry.
        /// </summary>
        /// <param name="fileId">The BucketFile ID</param>
        /// <param name="providerCode">The provider code</param>
        public void ClearFileUrlCache(int fileId, string providerCode)
        {
            var cacheKey = $"{KeyFileUrl}_{fileId}_{providerCode}";
            _memoryCache.Remove(cacheKey);
        }

        /// <summary>
        /// Clears all cached file URL entries for a specific file ID.
        /// </summary>
        /// <param name="fileId">The BucketFile ID</param>
        public void ClearFileUrlCachesByFileId(int fileId)
        {
            if (_memoryCache.TryGetValue(KeyFileUrlTracker, out ConcurrentBag<string>? trackedKeys))
            {
                var filePrefix = $"{KeyFileUrl}_{fileId}_";
                var keysToRemove = trackedKeys.Where(key => key.StartsWith(filePrefix)).ToList();

                foreach (var key in keysToRemove)
                {
                    _memoryCache.Remove(key);
                }
            }
        }

        /// <summary>
        /// Clears all cached file URL entries for a specific provider code.
        /// </summary>
        /// <param name="providerCode">The provider code</param>
        public void ClearFileUrlCachesByProviderCode(string providerCode)
        {
            if (_memoryCache.TryGetValue(KeyFileUrlTracker, out ConcurrentBag<string>? trackedKeys))
            {
                var providerSuffix = $"_{providerCode}";
                var keysToRemove = trackedKeys.Where(key => key.EndsWith(providerSuffix)).ToList();

                foreach (var key in keysToRemove)
                {
                    _memoryCache.Remove(key);
                }
            }
        }
    }
}