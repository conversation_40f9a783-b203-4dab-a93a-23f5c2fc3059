using System.Collections.Generic;
using System.Threading.Tasks;
using HolyBless.Albums.Dtos;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace HolyBless.Albums
{
    public interface IReadOnlyAlbumAppService : IApplicationService
    {
        Task<AlbumDto> GetAsync(int id);

        Task<PagedResultDto<AlbumDto>> GetListAsync(AlbumSearchDto input);

        Task<List<AlbumFileDto>> GetAlbumFilesAsync(int albumId);

    }
}
