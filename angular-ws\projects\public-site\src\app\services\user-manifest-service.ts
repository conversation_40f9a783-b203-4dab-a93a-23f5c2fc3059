import { effect, Injectable, signal } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { PrimeNG } from 'primeng/config';
import { UserManifest } from '@/providers/types/user-manifest';
import { NetworkUtil } from '@/commons/utilities/network.util';

export enum AudioDeviceTypes {
  'cmn' = 'cmn',
  'yue' = 'yue',
  'eng' = 'eng',
}

export enum EyeTypes {
  'zh-Hans' = 'zh-Hans',
  'zh-Hant' = 'zh-Hant',
  'en' = 'en',
}

@Injectable({ providedIn: 'root' })
export class UserManifestService {
  currentAudioDevice = signal(AudioDeviceTypes.eng);
  currentEye = signal(EyeTypes.en);

  // User manifest data signals
  private _ipAddress = signal<string>('');
  private _visitFromCountry = signal<string>('');
  private _hasUserAgreement = signal<boolean>(false);
  private _offlineMode = signal<boolean>(false);
  private _theme = signal<string>('light');
  private _notificationsEnabled = signal<boolean>(false);
  private _lastVisitedTime = signal<Date | undefined>(undefined);

  // Readonly accessors for the user manifest
  readonly ipAddress = this._ipAddress.asReadonly();
  readonly visitFromCountry = this._visitFromCountry.asReadonly();
  readonly hasUserAgreement = this._hasUserAgreement.asReadonly();
  readonly offlineMode = this._offlineMode.asReadonly();
  readonly theme = this._theme.asReadonly();
  readonly notificationsEnabled = this._notificationsEnabled.asReadonly();
  readonly lastVisitedTime = this._lastVisitedTime.asReadonly();

  constructor(
    private primeng: PrimeNG,
    private translateService: TranslateService,
  ) {
    this.initializeUserManifest();
  }

  setDefaultLang(lang: string) {
    this.translateService.setDefaultLang(lang);
  }

  translate(lang: string) {
    this.translateService.use(lang);
    this.translateService
      .get('primeng')
      .subscribe((res) => this.primeng.setTranslation(res));
  }

  updateAudioDevice(audio: AudioDeviceTypes) {
    this.currentAudioDevice.set(audio);
  }

  updateEye(eye: EyeTypes) {
    this.currentEye.set(eye);
  }

  /**
   * Initialize user manifest data including IP and country detection
   */
  private async initializeUserManifest(): Promise<void> {
    // Load saved preferences from localStorage
    this.loadUserPreferences();

    // Update last visited time
    this._lastVisitedTime.set(new Date());

    // Get IP and country if user has agreed to tracking
    if (this._hasUserAgreement()) {
      await this.updateIPAndCountry();
    }

    // Save updated preferences
    this.saveUserPreferences();
  }

  /**
   * Update IP address and country using NetworkUtil
   */
  async updateIPAndCountry(): Promise<void> {
    try {
      const { ip, country } = await NetworkUtil.getBrowserIPAndCountry();
      this._ipAddress.set(ip);
      this._visitFromCountry.set(country);
      console.log('Updated IP and Country:', { ip, country });
    } catch (error) {
      console.error('Failed to update IP and country:', error);
    }
  }

  /**
   * Set user agreement for tracking
   */
  setUserAgreement(agreed: boolean): void {
    this._hasUserAgreement.set(agreed);
    if (agreed) {
      this.updateIPAndCountry();
    } else {
      // Clear tracking data if user doesn't agree
      this._ipAddress.set('');
      this._visitFromCountry.set('');
    }
    this.saveUserPreferences();
  }

  /**
   * Update offline mode
   */
  setOfflineMode(offline: boolean): void {
    this._offlineMode.set(offline);
    this.saveUserPreferences();
  }

  /**
   * Update theme
   */
  setTheme(theme: string): void {
    this._theme.set(theme);
    this.saveUserPreferences();
  }

  /**
   * Update notifications enabled
   */
  setNotificationsEnabled(enabled: boolean): void {
    this._notificationsEnabled.set(enabled);
    this.saveUserPreferences();
  }

  /**
   * Get user manifest data for API headers
   */
  getUserManifestForHeaders(): Record<string, string> {
    const headers: Record<string, string> = {};

    if (this._hasUserAgreement()) {
      if (this._ipAddress()) {
        headers['X-User-IP'] = this._ipAddress();
      }
      if (this._visitFromCountry()) {
        headers['X-User-Country'] = this._visitFromCountry();
      }
      if (this._lastVisitedTime()) {
        headers['X-User-Last-Visit'] = this._lastVisitedTime()!.toISOString();
      }
    }

    headers['X-User-Language-Reading'] = this.currentEye().toString();
    headers['X-User-Language-Audio'] = this.currentAudioDevice().toString();
    headers['X-User-Offline-Mode'] = this._offlineMode().toString();
    headers['X-User-Theme'] = this._theme();
    headers['X-User-Notifications'] = this._notificationsEnabled().toString();

    return headers;
  }

  /**
   * Load user preferences from localStorage
   */
  private loadUserPreferences(): void {
    try {
      const saved = localStorage.getItem('userManifest');
      if (saved) {
        const data = JSON.parse(saved);
        this._hasUserAgreement.set(data.hasUserAgreement || false);
        this._offlineMode.set(data.offlineMode || false);
        this._theme.set(data.theme || 'light');
        this._notificationsEnabled.set(data.notificationsEnabled || false);

        if (data.hasUserAgreement) {
          this._ipAddress.set(data.ipAddress || '');
          this._visitFromCountry.set(data.visitFromCountry || '');
        }

        if (data.lastVisitedTime) {
          this._lastVisitedTime.set(new Date(data.lastVisitedTime));
        }
      }
    } catch (error) {
      console.error('Failed to load user preferences:', error);
    }
  }

  /**
   * Save user preferences to localStorage
   */
  private saveUserPreferences(): void {
    try {
      const data = {
        hasUserAgreement: this._hasUserAgreement(),
        offlineMode: this._offlineMode(),
        theme: this._theme(),
        notificationsEnabled: this._notificationsEnabled(),
        ipAddress: this._hasUserAgreement() ? this._ipAddress() : '',
        visitFromCountry: this._hasUserAgreement()
          ? this._visitFromCountry()
          : '',
        lastVisitedTime: this._lastVisitedTime()?.toISOString(),
      };
      localStorage.setItem('userManifest', JSON.stringify(data));
    } catch (error) {
      console.error('Failed to save user preferences:', error);
    }
  }
}
