import { Component, inject } from '@angular/core';
import { RouterOutlet } from '@angular/router';
import { CommonModule } from '@angular/common';
import { NavigationMenuComponent } from './components/navigation-menu/navigation-menu.component';
import { DrawerComponent } from './components/drawer/drawer.component';
import { DrawerService } from './services/drawer.service';

@Component({
  selector: 'app-root',
  imports: [
    RouterOutlet,
    CommonModule,
    NavigationMenuComponent,
    DrawerComponent,
  ],
  template: `
    <div class="app-container" [class.drawer-open]="drawerService.isVisible()">
      <!-- Navigation Menu Component -->
      <app-navigation-menu></app-navigation-menu>

      <!-- 主要内容区域 -->
      <main class="app-content">
        <router-outlet></router-outlet>
      </main>
    </div>
    
    <!-- Drawer 组件 -->
    <app-drawer></app-drawer>
  `,
  styles: [
    `
      .app-container {
        min-height: 100vh;
        display: flex;
        flex-direction: column;
        position: relative;
      }

      .app-content {
        flex: 1;
        display: flex;
      }

      /* 当 drawer 打开时添加遮罩效果 */
      .app-container.drawer-open::before {
        content: '';
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.3);
        z-index: 1000;
        pointer-events: auto;
      }
    `,
  ],
})
export class AppComponent {
  title = 'Holybless';

  // 注入 DrawerService
  public drawerService = inject(DrawerService);
}
