using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;
using HolyBless.Services;
using Shouldly;
using Xunit;
using Microsoft.Extensions.Primitives;
using System.Collections.Generic;

namespace HolyBless.Services
{
    public class RequestContextServiceTests : HolyBlessApplicationTestBase<HolyBlessApplicationTestModule>
    {
        private readonly IRequestContextService _requestContextService;
        private readonly IHttpContextAccessor _httpContextAccessor;

        public RequestContextServiceTests()
        {
            _requestContextService = GetRequiredService<IRequestContextService>();
            _httpContextAccessor = GetRequiredService<IHttpContextAccessor>();
        }

        [Fact]
        public void GetLanguageCode_WhenHeaderExists_ShouldReturnValue()
        {
            // Arrange
            var expectedLanguageCode = "en-US";
            SetupHttpContext(new Dictionary<string, StringValues>
            {
                { "LanguageCode", expectedLanguageCode }
            });

            // Act
            var result = _requestContextService.GetLanguageCode();

            // Assert
            result.ShouldBe(expectedLanguageCode);
        }

        [Fact]
        public void GetSpokenLangCode_WhenHeaderExists_ShouldReturnValue()
        {
            // Arrange
            var expectedSpokenLangCode = "en";
            SetupHttpContext(new Dictionary<string, StringValues>
            {
                { "SpokenLangCode", expectedSpokenLangCode }
            });

            // Act
            var result = _requestContextService.GetSpokenLangCode();

            // Assert
            result.ShouldBe(expectedSpokenLangCode);
        }

        [Fact]
        public void GetLanguageCodes_WhenBothHeadersExist_ShouldReturnBothValues()
        {
            // Arrange
            var expectedLanguageCode = "zh-CN";
            var expectedSpokenLangCode = "zh";
            SetupHttpContext(new Dictionary<string, StringValues>
            {
                { "LanguageCode", expectedLanguageCode },
                { "SpokenLangCode", expectedSpokenLangCode }
            });

            // Act
            var (languageCode, spokenLangCode) = _requestContextService.GetLanguageCodes();

            // Assert
            languageCode.ShouldBe(expectedLanguageCode);
            spokenLangCode.ShouldBe(expectedSpokenLangCode);
        }

        [Fact]
        public void GetLanguageCode_WhenHeaderDoesNotExist_ShouldReturnNull()
        {
            // Arrange
            SetupHttpContext(new Dictionary<string, StringValues>());

            // Act
            var result = _requestContextService.GetLanguageCode();

            // Assert
            result.ShouldBeNull();
        }

        [Fact]
        public void GetSpokenLangCode_WhenHeaderDoesNotExist_ShouldReturnNull()
        {
            // Arrange
            SetupHttpContext(new Dictionary<string, StringValues>());

            // Act
            var result = _requestContextService.GetSpokenLangCode();

            // Assert
            result.ShouldBeNull();
        }

        [Fact]
        public void GetLanguageCodes_WhenNoHttpContext_ShouldReturnNulls()
        {
            // Arrange
            _httpContextAccessor.HttpContext = null;

            // Act
            var (languageCode, spokenLangCode) = _requestContextService.GetLanguageCodes();

            // Assert
            languageCode.ShouldBeNull();
            spokenLangCode.ShouldBeNull();
        }

        private void SetupHttpContext(Dictionary<string, StringValues> headers)
        {
            var httpContext = new DefaultHttpContext();
            foreach (var header in headers)
            {
                httpContext.Request.Headers[header.Key] = header.Value;
            }
            _httpContextAccessor.HttpContext = httpContext;
        }
    }
}