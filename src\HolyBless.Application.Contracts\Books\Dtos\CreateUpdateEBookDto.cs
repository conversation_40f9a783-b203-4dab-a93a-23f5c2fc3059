﻿using System;
using System.ComponentModel.DataAnnotations;
using HolyBless.Enums;

namespace HolyBless.Books.Dtos;

public class CreateUpdateEBookDto
{
    [Required]
    [StringLength(128)]
    public string Title { get; set; } = string.Empty;

    [StringLength(500)]
    public string? Description { get; set; }

    public int Weight { get; set; } = 0;

    public int? ChannelId { get; set; }

    [Required]
    public BookType Type { get; set; } = BookType.Undefined;

    [Required]
    [DataType(DataType.Date)]
    public DateTime DeliveryDate { get; set; } = DateTime.Now;

    [Required]
    public int? ThumbnailFileId { get; set; }  //Thumbnail Image FileName

    public int Views { get; set; }
    public int Likes { get; set; }

    public string? LanguageCode { get; set; }
}