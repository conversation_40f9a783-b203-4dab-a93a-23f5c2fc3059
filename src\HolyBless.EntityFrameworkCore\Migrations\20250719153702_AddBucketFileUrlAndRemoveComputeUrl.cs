﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace HolyBless.Migrations
{
    /// <inheritdoc />
    public partial class AddBucketFileUrlAndRemoveComputeUrl : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "ComputeUrl",
                table: "BucketFiles");

            migrationBuilder.CreateTable(
                name: "BucketFileUrls",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    BucketFileId = table.Column<int>(type: "integer", nullable: false),
                    ComputeUrl = table.Column<string>(type: "character varying(256)", unicode: false, maxLength: 256, nullable: false),
                    ProviderCode = table.Column<string>(type: "character varying(20)", unicode: false, maxLength: 20, nullable: false),
                    ExtraProperties = table.Column<string>(type: "text", nullable: false),
                    ConcurrencyStamp = table.Column<string>(type: "character varying(40)", maxLength: 40, nullable: false),
                    CreationTime = table.Column<DateTime>(type: "timestamp without time zone", nullable: false),
                    CreatorId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModificationTime = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    LastModifierId = table.Column<Guid>(type: "uuid", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_BucketFileUrls", x => x.Id);
                    table.ForeignKey(
                        name: "FK_BucketFileUrls_BucketFiles_BucketFileId",
                        column: x => x.BucketFileId,
                        principalTable: "BucketFiles",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_BucketFileUrls_BucketFileId_ProviderCode",
                table: "BucketFileUrls",
                columns: new[] { "BucketFileId", "ProviderCode" },
                unique: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "BucketFileUrls");

            migrationBuilder.AddColumn<string>(
                name: "ComputeUrl",
                table: "BucketFiles",
                type: "character varying(256)",
                unicode: false,
                maxLength: 256,
                nullable: true);
        }
    }
}
