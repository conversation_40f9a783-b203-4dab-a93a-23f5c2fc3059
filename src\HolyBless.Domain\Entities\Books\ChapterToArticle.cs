﻿using HolyBless.Entities.Articles;
using Volo.Abp;
using Volo.Abp.Domain.Entities;

namespace HolyBless.Entities.Books
{
    public class ChapterToArticle : Entity, ISoftDelete
    {
        public int ChapterId { get; set; }
        public Chapter Chapter { get; set; } = default!;
        public int ArticleId { get; set; }
        public Article Article { get; set; } = default!;
        public int Weight { get; set; } = 0;
        public bool IsDeleted { get; set; }

        public override object?[] GetKeys()
        {
            return [ChapterId, ArticleId];
        }
    }
}