﻿using HolyBless.Entities.Buckets;
using HolyBless.Entities.Channels;
using HolyBless.Enums;
using System.Collections.Generic;

namespace HolyBless.Entities.Albums
{
    public class Album : DomainAuditedAggregateRoot<int>
    {
        public int? ChannelId { get; set; }
        public Channel? Channel { get; set; }
        public int? ThumbnailFileId { get; set; }  //Thumbnail Image FileName
        public BucketFile? ThumbnailBucketFile { get; set; }
        public string Title { get; set; } = default!; //Title of the album
        public string? Description { get; set; }
        public int Views { get; set; } = 0;          //Visit times
        public int Likes { get; set; } = 0;          //Like times
        public int Weight { get; set; } = 0;          //Weight of the album, used for sorting

        public AlbumType AlbumType { get; set; }
        public string? SpokenLangCode { get; set; } // Spoken language code for the album
        // Navigation property for many-to-many relationship with BucketFile
        public ICollection<AlbumToFile> AlbumToFiles { get; set; } = new List<AlbumToFile>();

        public Album()
        {
            
        }

        public Album(int id):base(id)
        {
            
        }
    }
}
