﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using HolyBless.Entities.Books;
using HolyBless.Entities.Buckets;
using HolyBless.Entities.Collections;
using HolyBless.Entities.VirtualFolders;
using HolyBless.Enums;
using HolyBless.Lookups;
using Volo.Abp.Domain.Entities.Auditing;

namespace HolyBless.Entities.Articles
{
    public class Article : LanguageAuditedAggregateRoot<int>
    {
        public string Title { get; set; } = default!;
        public int? ThumbnailFileId { get; set; }  //Thumbnail Image FileName
        public BucketFile? ThumbnailBucketFile { get; set; }
        public string? Description { get; set; }     //Description of the article, when show in list page
        public string? Keywords { get; set; }        //Keywords of the article for SEO
        public int Views { get; set; } = 0;          //Visit times
        public int Likes { get; set; } = 0;          //Like times

        //Use this property to replace content code to indicate it's same article across different language
        public DateTime DeliveryDate { get; set; }

        public ArticleContentCategory ArticleContentCategory { get; set; } = ArticleContentCategory.Article;

        public PublishStatus Status { get; set; } = PublishStatus.Draft;

        //The content of the article
        public string? Content { get; set; }

        public string? Memo { get; set; }

        //Article Media and Article Attachment are put in a virtual folder
        public VirtualDiskFolder? VirtualDiskFolder { get; set; }

        public ICollection<ArticleToTag> ArticleToTags { get; set; } = [];
        public ICollection<CollectionToArticle> CollectionToArticles { get; set; } = [];
        public ICollection<ArticleFile> ArticleFiles { get; set; } = [];
        public ICollection<ChapterToArticle> ChapterToArticles { get; set; } = [];

        public Article()
        {
        }
    }
}