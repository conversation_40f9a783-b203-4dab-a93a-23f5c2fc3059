import { RestService, Rest } from '@abp/ng.core';
import { Injectable } from '@angular/core';
import type { IActionResult } from '../../microsoft/asp-net-core/mvc/models';

@Injectable({
  providedIn: 'root',
})
export class RequestContextService {
  apiName = 'Default';
  

  getLanguageInfo = (config?: Partial<Rest.Config>) =>
    this.restService.request<any, IActionResult>({
      method: 'GET',
      url: '/api/RequestContext/language-info',
    },
    { apiName: this.apiName,...config });
  

  getLocalizedMessage = (config?: Partial<Rest.Config>) =>
    this.restService.request<any, IActionResult>({
      method: 'GET',
      url: '/api/RequestContext/localized-message',
    },
    { apiName: this.apiName,...config });

  constructor(private restService: RestService) {}
}
