using HolyBless.Books.Dtos;
using System.Collections.Generic;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace HolyBless.Books;

public interface IChapterAppService :
    ICrudAppService< //Defines CRUD methods
        ChapterDto, //Used to show chapters
        int, //Primary key of the chapter entity
        PagedAndSortedResultRequestDto, //Used for paging/sorting
        CreateUpdateChapterDto> //Used to create/update a chapter
{
    Task<List<ChapterDto>> GetChaptersByEBookIdAsync(int eBookId);
    Task<List<ChapterDto>> GetChildChaptersAsync(int parentChapterId);
    Task<List<ChapterToArticleDto>> GetChapterArticlesAsync(int chapterId);
    Task<ChapterToArticleDto> AddArticleToChapterAsync(CreateUpdateChapterToArticleDto input);
    Task RemoveArticleFromChapterAsync(int chapterId, int articleId);
}
