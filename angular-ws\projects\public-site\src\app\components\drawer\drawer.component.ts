import { Component, computed, inject, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DrawerModule } from 'primeng/drawer';
import { DividerModule } from 'primeng/divider';
import { ButtonModule } from 'primeng/button';
import { DrawerService } from '../../services/drawer.service';
import { MenuItem } from 'primeng/api';
import { MenuModule } from 'primeng/menu';
import { I18nService } from '@/services/i18n.service';

@Component({
  selector: 'app-drawer',
  standalone: true,
  imports: [
    CommonModule,
    DrawerModule,
    DividerModule,
    ButtonModule,
    MenuModule,
  ],
  templateUrl: './drawer.component.html',
  styleUrls: ['./drawer.component.scss'],
})
export class DrawerComponent {
  // 注入 DrawerService
  drawerService = inject(DrawerService);
  i18nService = inject(I18nService);

  audioLanguages = computed((): MenuItem[] =>
    this.i18nService.supportedAudioDevices.map((lang) => ({
      label: lang.label,
      command: () => this.i18nService.setAudioDevice(lang.code),
    })),
  );
}
