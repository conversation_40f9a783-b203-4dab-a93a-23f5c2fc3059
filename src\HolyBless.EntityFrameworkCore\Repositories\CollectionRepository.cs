﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using HolyBless.Entities.Collections;
using HolyBless.EntityFrameworkCore;
using HolyBless.Enums;
using HolyBless.Interfaces;
using HolyBless.Results;
using Microsoft.EntityFrameworkCore;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Domain.Repositories.EntityFrameworkCore;
using Volo.Abp.EntityFrameworkCore;

namespace HolyBless.Repositories
{
    public class CollectionRepository : EfCoreRepository<HolyBlessDbContext, Collection, int>, ICollectionRepository
    {
        public CollectionRepository(
            IDbContextProvider<HolyBlessDbContext> dbContextProvider
            ) : base(dbContextProvider)
        {
        }

        public async Task<CollectionSummaryResult> GetCollectionSummaryAsync(int collectionId, CollectionSummaryRequest request)
        {
            var result = new CollectionSummaryResult();
            var collectionRecord = await this.FirstOrDefaultAsync(c => c.Id == collectionId);
            if (collectionRecord == null)
            {
                return result;
            }
            result.Id = collectionRecord.Id;
            result.Name = collectionRecord.Name;
            result.Description = collectionRecord.Description ?? "";
            result.OrderByField = collectionRecord.DefaultOrderBy;
            if (string.IsNullOrWhiteSpace(request.Sorting))
            {
                request.Sorting = collectionRecord.DefaultOrderBy.ToString();
            }
            var dbContext = await GetDbContextAsync();

            var query = from ca in dbContext.CollectionToArticles
                        join a in dbContext.Articles on ca.ArticleId equals a.Id
                        join f in dbContext.BucketFiles on a.ThumbnailFileId equals f.Id into thumbnails
                        from th in thumbnails.DefaultIfEmpty()
                        join u in dbContext.BucketFileUrls on th.Id equals u.BucketFileId into urls
                        from url in urls.DefaultIfEmpty()
                        where ca.CollectionId == collectionRecord.Id && a.Status == PublishStatus.Published
                        select new ArticleSummaryResult
                        {
                            Id = a.Id,
                            Title = a.Title,
                            Description = a.Description ?? "",
                            ThumbnailUrl = url != null && url.ProviderCode == ProviderCodeConstants.CloudFlare ? url.ComputeUrl : null,
                            CreationTime = a.CreationTime,
                            LastModificationTime = a.LastModificationTime == null ? a.CreationTime : a.LastModificationTime.Value,
                            DeliveryDate = a.DeliveryDate,
                        };
            if (request.Year.HasValue)
            {
                if (request.Sorting.StartsWith("CreationTime", StringComparison.OrdinalIgnoreCase))
                {
                    result.OrderByField = DefaultOrderByField.CreationTime;
                    query = query.Where(a => a.CreationTime.Year == request.Year.Value)
                        .WhereIf(request.Month.HasValue, a => a.CreationTime.Month == request.Month.Value);
                }
                else if (request.Sorting.StartsWith("DeliveryDate", StringComparison.OrdinalIgnoreCase))
                {
                    result.OrderByField = DefaultOrderByField.DeiveryDate;
                    query = query.Where(a => a.DeliveryDate.Year == request.Year.Value)
                        .WhereIf(request.Month.HasValue, a => a.DeliveryDate.Month == request.Month.Value);
                }
                else if (request.Sorting.StartsWith("LastModificationTime", StringComparison.OrdinalIgnoreCase))
                {
                    result.OrderByField = DefaultOrderByField.LastModificationTime;
                    query = query.Where(a => a.LastModificationTime.Year == request.Year.Value)
                        .WhereIf(request.Month.HasValue, a => a.LastModificationTime.Month == request.Month.Value);
                }
            }

            result.TotalRecords = await query.CountAsync();
            if (!request.Sorting.EndsWith(" desc", StringComparison.InvariantCultureIgnoreCase))
            {
                request.Sorting += " desc";
            }
            result.Articles = await query.OrderBy(request.Sorting)
                         .Skip(request.Skip)
                         .Take(request.MaxResultCount)
                         .ToListAsync();

            return result;
        }

        public async Task<List<Collection>> GetAllDescendantsAsync(int collectionId)
        {
            var dbContext = await GetDbContextAsync();
            
            // More efficient LINQ approach: Build hierarchy level by level
            var result = new List<Collection>();
            var currentLevelParentIds = new List<int> { collectionId };
            
            while (currentLevelParentIds.Any())
            {
                // Get children of current level parents
                var children = await dbContext.Collections
                    .Where(x => x.ParentCollectionId.HasValue && currentLevelParentIds.Contains(x.ParentCollectionId.Value))
                    .OrderByDescending (x=>x.Weight)
                    .ToListAsync();
                
                if (!children.Any())
                    break;
                    
                result.AddRange(children);
                currentLevelParentIds = children.Select(x => x.Id).ToList();
            }
            
            return result.OrderBy(x => x.ParentCollectionId).ThenBy(x => x.Name).ToList();
        }
    }
}