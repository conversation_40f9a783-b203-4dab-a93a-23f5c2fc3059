using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using HolyBless.Albums.Dtos;
using HolyBless.Entities.Albums;
using HolyBless.Entities.Buckets;
using HolyBless.Entities.Channels;
using HolyBless.Permissions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using Volo.Abp;
using Volo.Abp.Domain.Entities;
using Volo.Abp.Domain.Repositories;

namespace HolyBless.Albums
{
    [Authorize(HolyBlessPermissions.Albums.Default)]
    public class AlbumAppService : ReadOnlyAlbumAppService, IAlbumAppService
    {
        public AlbumAppService(
            IRepository<Album, int> albumRepository,
            IRepository<AlbumToFile> albumToFileRepository,
            IRepository<BucketFile, int> bucketFileRepository,
            IRepository<Channel, int> channelRepository)
            : base(albumRepository, albumToFileRepository, bucketFileRepository, channelRepository)
        {
        }

        [Authorize(HolyBlessPermissions.Albums.Create)]
        public async Task<AlbumDto> CreateAsync(CreateUpdateAlbumDto input)
        {
            var album = ObjectMapper.Map<CreateUpdateAlbumDto, Album>(input);
            album = await _albumRepository.InsertAsync(album, autoSave: true);
            return ObjectMapper.Map<Album, AlbumDto>(album);
        }

        [Authorize(HolyBlessPermissions.Albums.Edit)]
        public async Task<AlbumDto> UpdateAsync(int id, CreateUpdateAlbumDto input)
        {
            var album = await _albumRepository.GetAsync(id);
            ObjectMapper.Map(input, album);
            album = await _albumRepository.UpdateAsync(album, autoSave: true);
            return ObjectMapper.Map<Album, AlbumDto>(album);
        }

        [Authorize(HolyBlessPermissions.Albums.Delete)]
        public async Task DeleteAsync(int id)
        {
            await _albumRepository.DeleteAsync(id);
        }

        [Authorize(HolyBlessPermissions.Albums.Edit)]
        public async Task<List<AlbumFileDto>> AddFilesToAlbumAsync(int albumId, List<CreateUpdateAlbumToFileDto> files)
        {
            // Verify album exists
            _ = await _albumRepository.GetAsync(albumId);

            var albumToFiles = new List<AlbumToFile>();

            foreach (var fileDto in files)
            {
                // Check if file already exists in album
                var existingAlbumFile = await _albumToFileRepository.FirstOrDefaultAsync(
                    af => af.AlbumId == albumId && af.FileId == fileDto.FileId);

                if (existingAlbumFile == null)
                {
                    // Verify bucket file exists
                    _ = await _bucketFileRepository.GetAsync(fileDto.FileId);

                    var albumToFile = new AlbumToFile
                    {
                        AlbumId = albumId,
                        FileId = fileDto.FileId,
                        Title = fileDto.Title,
                        Weight = fileDto.Weight
                    };

                    albumToFiles.Add(await _albumToFileRepository.InsertAsync(albumToFile));
                }
            }

            return ObjectMapper.Map<List<AlbumToFile>, List<AlbumFileDto>>(albumToFiles);
        }

        [Authorize(HolyBlessPermissions.Albums.Edit)]
        public async Task RemoveFileFromAlbumAsync(int albumId, int fileId)
        {
            var albumToFile = await _albumToFileRepository.FirstOrDefaultAsync(
                af => af.AlbumId == albumId && af.FileId == fileId);

            if (albumToFile != null)
            {
                await _albumToFileRepository.DeleteAsync(albumToFile);
            }
        }

        [Authorize(HolyBlessPermissions.Albums.Edit)]
        public async Task<AlbumFileDto> UpdateAlbumFileAsync(int albumId, int fileId, CreateUpdateAlbumToFileDto input)
        {
            var albumToFile = await _albumToFileRepository.FirstOrDefaultAsync(
                af => af.AlbumId == albumId && af.FileId == fileId);

            if (albumToFile == null)
            {
                throw new EntityNotFoundException(typeof(AlbumToFile), $"AlbumId: {albumId}, FileId: {fileId}");
            }

            ObjectMapper.Map(input, albumToFile);
            albumToFile = await _albumToFileRepository.UpdateAsync(albumToFile, autoSave: true);

            return ObjectMapper.Map<AlbumToFile, AlbumFileDto>(albumToFile);
        }

        [Authorize(HolyBlessPermissions.Albums.Edit)]
        public async Task ReorderAlbumFilesAsync(int albumId, List<int> fileIds)
        {
            var albumFiles = await _albumToFileRepository.GetListAsync(
                af => af.AlbumId == albumId && fileIds.Contains(af.FileId));

            for (int i = 0; i < fileIds.Count; i++)
            {
                var albumFile = albumFiles.FirstOrDefault(af => af.FileId == fileIds[i]);
                if (albumFile != null)
                {
                    albumFile.Weight = i;
                    await _albumToFileRepository.UpdateAsync(albumFile);
                }
            }
        }
    }
}