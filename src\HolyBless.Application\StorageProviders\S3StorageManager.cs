﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Amazon.S3;
using Amazon.S3.Model;
using HolyBless.Configs;
using HolyBless.Entities.Buckets;
using HolyBless.Enums;
using HolyBless.Helpers;
using HolyBless.StorageProviders.Dtos;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Volo.Abp;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Uow;

namespace HolyBless.StorageProviders
{
    public class S3StorageManager : ApplicationService, IS3StorageManager
    {
        private readonly IRepository<StorageBucket> _bucketRepository;
        private readonly IRepository<ProviderSecret> _providerSecretRepository;
        private readonly IRepository<BucketFile> _bucketFileRepository;
        private readonly IRepository<BucketFileUrl> _bucketFileUrlRepository;
        private readonly IStorageProviderAppService _storageProviderAppService;
        private readonly IUnitOfWorkManager _unitOfWorkManager;
        private readonly AppConfig _settings;

        public S3StorageManager
            (
            IRepository<StorageBucket> bucketRepository
            , IRepository<ProviderSecret> providerSecretRepository
            , IRepository<BucketFile> bucketFileRepository
            , IRepository<BucketFileUrl> bucketFileUrlRepository
            , IStorageProviderAppService storageProviderAppService
            , IUnitOfWorkManager unitOfWorkManager
            , IOptions<AppConfig> settings
            )
        {
            _bucketRepository = bucketRepository;
            _providerSecretRepository = providerSecretRepository;
            _bucketFileRepository = bucketFileRepository;
            _bucketFileUrlRepository = bucketFileUrlRepository;
            _storageProviderAppService = storageProviderAppService;
            _unitOfWorkManager = unitOfWorkManager;
            _settings = settings.Value;
        }

        private async Task<List<string>> ListBucketsAsync(ProviderSecret providerSecret)
        {
            return await ListBucketsAsync(providerSecret.AccessId, providerSecret.AccessSecretKey, providerSecret.ApiEndPoint);
        }

        private async Task<List<string>> ListBucketsAsync(string accessId, string accessSecretKey, string apiEndPoint)
        {
            var bucketNames = new List<string>();
            try
            {
                using var s3Client = GetS3Client(accessId, accessSecretKey, apiEndPoint);
                var response = await s3Client.ListBucketsAsync();
                foreach (var bucket in response.Buckets)
                {
                    bucketNames.Add(bucket.BucketName);
                }
            }
            catch (Exception ex)
            {
                Logger.LogException(ex);
                throw;
            }

            return bucketNames;
        }

        private async Task<StorageBucket> GetBucketById(int bucketId)
        {
            var query = await _bucketRepository.GetQueryableAsync();
            var bucket = await query.Include(x => x.StorageProvider).FirstOrDefaultAsync(x => x.Id == bucketId);
            Check.NotNull(bucket, nameof(StorageBucket));
            return bucket;
        }

        private AmazonS3Client GetS3Client(ProviderSecret providerSecret)
        {
            return GetS3Client(providerSecret.AccessId, providerSecret.AccessSecretKey, providerSecret.ApiEndPoint);
        }

        private AmazonS3Client GetS3Client(string accessId, string accessSecretKey, string apiEndPoint)
        {
            var config = new AmazonS3Config
            {
                ServiceURL = apiEndPoint,
                ForcePathStyle = false // AliYun requires path-style addressing to false
            };

            if (apiEndPoint.Contains(".r2.", StringComparison.OrdinalIgnoreCase))
            {
                config.ForcePathStyle = true; //R2 requires path-style addressing to true
            }
            var s3Client = new AmazonS3Client(accessId, accessSecretKey, config);
            return s3Client;
        }

        [HttpGet]
        public async Task<List<string>> ListSubfoldersAsync(int bucketId, string prefix = "")
        {
            // NOTE: This method will not work as AccessId, AccessSecretKey, and ApiEndPoint properties have been commented out in StorageProvider class
            var subfolders = new List<string>();

            try
            {
                var bucket = await GetBucketById(bucketId);
                var provider = bucket.StorageProvider;
                var request = new ListObjectsV2Request
                {
                    BucketName = bucket.BucketName,
                    Prefix = prefix,         // Start from a given prefix
                    Delimiter = "/"          // Treat `/` as a folder separator
                };

                // These properties are no longer available in StorageProvider class
                // var s3Client = GetS3Client(provider.AccessId, provider.AccessSecretKey, provider.ApiEndPoint);
                // var response = await s3Client.ListObjectsV2Async(request);

                throw new NotImplementedException("AccessId, AccessSecretKey, and ApiEndPoint properties are no longer available in StorageProvider class");

                // Fetch common prefixes (subfolders)
                // subfolders.AddRange(response.CommonPrefixes);

                // Optionally, print objects (files) at the current level
                // foreach (var obj in response.S3Objects)
                // {
                //     Console.WriteLine($"File: {obj.Key}");
                // }
            }
            catch (Exception ex)
            {
                Logger.LogException(ex);
                throw;
            }

            return subfolders;
        }

        [HttpGet]
        public async Task<List<string>> ListFilesAsync(int bucketId)
        {
            // NOTE: This method will not work as AccessId, AccessSecretKey, and ApiEndPoint properties have been commented out in StorageProvider class
            throw new NotImplementedException("AccessId, AccessSecretKey, and ApiEndPoint properties are no longer available in StorageProvider class");
        }

        [HttpGet]
        [Route("DownloadBucketFile")]
        public async Task<byte[]> DownloadBucketFile(int bucketId, string fileNamePath)
        {
            // NOTE: This method will not work as AccessId, AccessSecretKey, and ApiEndPoint properties have been commented out in StorageProvider class
            // MemoryStream memory = await DownloadFileAsync(bucketId, fileNamePath);
            // memory.Position = 0;

            // var contentType = "application/octet-stream"; // Change based on file type if needed
            // var fileName = Path.GetFileName(fileNamePath);
            // return new FileStreamResult(memory, contentType)
            // {
            //     FileDownloadName = fileName
            // };

            throw new NotImplementedException("AccessId, AccessSecretKey, and ApiEndPoint properties are no longer available in StorageProvider class");
        }

        public async Task<bool> UploadFileAsync(int bucketId, string subFolder, string fileName, Stream fileStream)
        {
            // NOTE: This method will not work as AccessId, AccessSecretKey, and ApiEndPoint properties have been commented out in StorageProvider class
            try
            {
                var bucket = await GetBucketById(bucketId);
                var provider = bucket.StorageProvider;
                // Combine subfolder and filename to create the object key
                var objectKey = $"{subFolder.TrimEnd('/')}/{Path.GetFileName(fileName)}";

                var request = new PutObjectRequest
                {
                    BucketName = bucket.BucketName,
                    Key = objectKey,
                    InputStream = fileStream,
                    ContentType = "application/octet-stream" // Set appropriate MIME type if known
                };

                // These properties are no longer available in StorageProvider class
                // var s3Client = GetS3Client(provider.AccessId, provider.AccessSecretKey, provider.ApiEndPoint);
                // var response = await s3Client.PutObjectAsync(request);

                throw new NotImplementedException("AccessId, AccessSecretKey, and ApiEndPoint properties are no longer available in StorageProvider class");

                // if (response.HttpStatusCode == System.Net.HttpStatusCode.OK)
                // {
                //     return true;
                // }
                // else
                // {
                //     Logger.LogError("Failed to upload file. StatusCode: {StatusCode}", response.HttpStatusCode);
                //     return false;
                // }
            }
            catch (Exception ex)
            {
                Logger.LogException(ex);
                throw;
            }
        }

        [ApiExplorerSettings(IgnoreApi = true)]
        public async Task<MemoryStream> DownloadFileAsync(int bucketId, string fieKey)
        {
            // NOTE: This method will not work as AccessId, AccessSecretKey, and ApiEndPoint properties have been commented out in StorageProvider class
            try
            {
                var bucket = await GetBucketById(bucketId);
                var provider = bucket.StorageProvider;
                var request = new GetObjectRequest
                {
                    BucketName = bucket.BucketName,
                    Key = fieKey
                };

                // These properties are no longer available in StorageProvider class
                // var s3Client = GetS3Client(provider.AccessId, provider.AccessSecretKey, provider.ApiEndPoint);
                // using (var response = await s3Client.GetObjectAsync(request))
                // {
                //     var memoryStream = new MemoryStream();
                //     await response.ResponseStream.CopyToAsync(memoryStream);
                //     return memoryStream;
                // }

                throw new NotImplementedException("AccessId, AccessSecretKey, and ApiEndPoint properties are no longer available in StorageProvider class");
            }
            catch (Exception ex)
            {
                Logger.LogException(ex);
                throw;
            }
        }

        /// <summary>
        /// Walks through all CloudFlare storage buckets and their files, updating the BucketFile table
        /// </summary>
        /// <returns>Number of files processed</returns>
        [HttpPost]
        public async Task<int> IndexCloudFlareFilesAsync()
        {
            var filesProcessed = 0;

            try
            {
                var processingStartTime = DateTime.UtcNow;
                // Get provider secret (assuming there's one secret for CloudFlare)
                var providerSecret = await GetProviderSecretAsync();
                var actualCfBuckets = await ListBucketsAsync(providerSecret);

                // Get all distinct StorageBuckets for CloudFlare providers matching current environment
                var cfBuckets = await GetCloudFlareStorageBucketsAsync();

                if (!cfBuckets.Any())
                {
                    Logger.LogWarning("No CloudFlare storage buckets found for environment: {Environment}", _settings.Environment);
                    return 0;
                }
                var distinctBuckets = cfBuckets.Where(x => actualCfBuckets.Contains(x.BucketName)).Select(x => new BucketStorageMeta
                {
                    BucketName = x.BucketName,
                    LanguageCode = x.LanguageCode,
                    SpokenLangCode = x.SpokenLangCode,
                    ContentType = x.ContentType,
                    SubDomain = x.SubDomain,
                    Domain = x.StorageProvider.BindedDomain ?? string.Empty,
                }).Distinct().ToList();

                // Process each bucket
                var groupedBuckets = distinctBuckets
                                   .GroupBy(bucket => bucket.BucketName)
                                   .ToList();

                foreach (var group in groupedBuckets)
                {
                    using var uow = _unitOfWorkManager.Begin(requiresNew: true, isTransactional: true);
                    var bucketName = group.Key;
                    var groupItems = group.ToList();
                    var bucketFilesProcessed = await ProcessBucketFilesAsync(bucketName, groupItems, providerSecret, ProviderCodeConstants.CloudFlare);
                    filesProcessed += bucketFilesProcessed;
                    await uow.SaveChangesAsync();
                    await uow.CompleteAsync();
                }
                await UpdateNotExistFiles(processingStartTime);
                Logger.LogInformation("Total files processed: {TotalFiles}", filesProcessed);
                return filesProcessed;
            }
            catch (Exception ex)
            {
                Logger.LogException(ex);
                throw;
            }
        }

        private async Task UpdateNotExistFiles(DateTime processTime)
        {
            var notExists = await _bucketFileRepository.GetListAsync(x => x.Environment == _settings.Environment &&
                    x.LastModificationTime < processTime);
            foreach (var file in notExists)
            {
                file.Exists = false;
            }
            await _bucketFileRepository.UpdateManyAsync(notExists);
        }

        /// <summary>
        /// Gets CloudFlare storage buckets using the optimized cached provider service.
        /// This method leverages StorageProviderAppService's GetCachedAllProviders for better performance.
        /// </summary>
        /// <returns>List of CloudFlare storage buckets with StorageProvider included</returns>
        private async Task<List<StorageBucket>> GetCloudFlareStorageBucketsAsync()
        {
            try
            {
                // Use the optimized cached bucket DTOs from StorageProviderAppService
                var bucketDtos = await _storageProviderAppService.GetCloudFlareBucketsAsync();

                if (!bucketDtos.Any())
                {
                    Logger.LogWarning("No CloudFlare buckets found via StorageProviderAppService for environment: {Environment}",
                        _settings.Environment);
                    return new List<StorageBucket>();
                }

                // Convert DTOs back to entities by fetching from repository with necessary includes
                var bucketIds = bucketDtos.Select(dto => dto.Id).ToList();
                var query = await _bucketRepository.GetQueryableAsync();

                var buckets = await query
                    .Include(x => x.StorageProvider) // Required for CloudFlare operations
                    .Where(x => bucketIds.Contains(x.Id))
                    .OrderBy(x => x.BucketName) // Consistent ordering
                    .ToListAsync();

                Logger.LogDebug("Retrieved {BucketCount} CloudFlare buckets from repository for synchronization",
                    buckets.Count);

                return buckets;
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error retrieving CloudFlare buckets for file synchronization");
                throw;
            }
        }

        private async Task<ProviderSecret> GetProviderSecretAsync()
        {
            // For now, get the first available provider secret
            // In a real scenario, you might want to link secrets to specific providers
            var secret = await _providerSecretRepository.FirstOrDefaultAsync();
            Check.NotNull(secret, nameof(ProviderSecret), "No provider secret found. Please configure CloudFlare credentials.");
            return secret;
        }

        private async Task<int> ProcessBucketFilesAsync(string bucketName, List<BucketStorageMeta> buckets, ProviderSecret secret, string providerCode)
        {
            var filesProcessed = 0;

            try
            {
                using var s3Client = GetS3Client(secret.AccessId, secret.AccessSecretKey, secret.ApiEndPoint);

                // List all objects in the bucket
                var request = new ListObjectsV2Request
                {
                    BucketName = bucketName,
                    MaxKeys = 1000 // Process in batches
                };

                ListObjectsV2Response response;
                do
                {
                    response = await s3Client.ListObjectsV2Async(request);

                    foreach (var s3Object in response.S3Objects)
                    {
                        // Skip folders (objects ending with /)
                        if (s3Object.Key.EndsWith('/'))
                            continue;

                        await ProcessSingleFileAsync(bucketName, buckets, s3Object, providerCode);
                        filesProcessed++;
                    }

                    // Set continuation token for next batch
                    request.ContinuationToken = response.NextContinuationToken;
                } while (response.IsTruncated);

                return filesProcessed;
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error processing files for bucket: {BucketName}", bucketName);
                throw;
            }
        }

        private async Task ProcessSingleFileAsync(string bucketName, List<BucketStorageMeta> buckets, S3Object s3Object, string providerCode)
        {
            try
            {
                // Extract file name and relative path from CloudFlare S3 object key
                var (fileName, relativePath) = ExtractFileNameAndPath(s3Object.Key);
                if (s3Object.Key.StartsWith(".recycle") || s3Object.Key.EndsWith("index.html", StringComparison.InvariantCultureIgnoreCase))
                {
                    return;
                }
                foreach (var bucket in buckets)
                {
                    // Check if file already exists in database
                    var existingFile = await _bucketFileRepository.FirstOrDefaultAsync(f =>
                        f.FileName == fileName &&
                        (f.LanguageCode ?? "") == (bucket.LanguageCode ?? "") &&
                        (f.SpokenLangCode ?? "") == (bucket.SpokenLangCode ?? "") &&
                        f.ContentCategory == bucket.ContentType &&
                        f.Environment == _settings.Environment);

                    if (existingFile != null)
                    {
                        // Update existing file with CloudFlare information
                        existingFile.Exists = true;
                        existingFile.LastModificationTime = s3Object.LastModified;

                        // Ensure FileName and RelativePathInBucket are correctly set from CloudFlare
                        existingFile.FileName = fileName;
                        existingFile.RelativePathInBucket = relativePath;
                        existingFile.LanguageCode = bucket.LanguageCode;
                        existingFile.SpokenLangCode = bucket.SpokenLangCode;
                        existingFile.ContentCategory = bucket.ContentType;
                        existingFile.Size = s3Object.Size;
                        existingFile.MediaType = MediaTypeHelper.GetMediaType(fileName);
                        await _bucketFileRepository.UpdateAsync(existingFile, true);

                        // Create or update BucketFileUrl record for CloudFlare
                        var computeUrl = CombineUrl(bucket.Domain, bucket.SubDomain, relativePath, fileName);
                        await InsertOrUpdateFileUrl(existingFile.Id, computeUrl, providerCode);

                        Logger.LogDebug("Updated existing BucketFile: {FileName} in path: {RelativePath}",
                            fileName, relativePath);
                    }
                    else
                    {
                        // Create new file record with properly extracted CloudFlare path information
                        var bucketFile = new BucketFile
                        {
                            FileName = fileName,
                            RelativePathInBucket = relativePath,
                            LanguageCode = bucket.LanguageCode,
                            SpokenLangCode = bucket.SpokenLangCode,
                            Environment = _settings.Environment,
                            Exists = true,
                            Size = s3Object.Size,
                            MediaType = MediaTypeHelper.GetMediaType(fileName),
                            ContentCategory = bucket.ContentType,
                            Views = 0
                        };

                        // Set LastModificationTime from S3 object
                        bucketFile.LastModifiedAtStorage = s3Object.LastModified;

                        bucketFile = await _bucketFileRepository.InsertAsync(bucketFile, true);

                        // Create BucketFileUrl record for CloudFlare
                        var computeUrl = CombineUrl(bucket.Domain, bucket.SubDomain, relativePath, fileName);
                        await InsertFileUrl(bucketFile.Id, computeUrl, providerCode);

                        Logger.LogDebug("Created new BucketFile: {FileName} in path: {RelativePath}",
                            fileName, relativePath);
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error processing file: {FileName} in bucket: {BucketName}",
                    s3Object.Key, bucketName);
                // Continue processing other files
            }
        }

        private async Task InsertFileUrl(int fileId, string computeUrl, string providerCode)
        {
            var bucketFileUrl = new BucketFileUrl
            {
                BucketFileId = fileId,
                ComputeUrl = computeUrl,
                ProviderCode = providerCode
            };
            await _bucketFileUrlRepository.InsertAsync(bucketFileUrl, true);
        }

        private async Task InsertOrUpdateFileUrl(int fileId, string computeUrl, string providerCode)
        {
            var existingUrl = await _bucketFileUrlRepository.FirstOrDefaultAsync(u =>
                            u.BucketFileId == fileId && u.ProviderCode == ProviderCodeConstants.CloudFlare);

            if (existingUrl != null)
            {
                existingUrl.ComputeUrl = computeUrl;
                existingUrl.ProviderCode = providerCode;
                await _bucketFileUrlRepository.UpdateAsync(existingUrl, true);
            }
            else
            {
                await InsertFileUrl(fileId, computeUrl, providerCode);
            }
        }

        /// <summary>
        /// Extracts the file name and relative path from CloudFlare S3 object key
        /// </summary>
        /// <param name="s3ObjectKey">The S3 object key (full path)</param>
        /// <returns>Tuple containing (fileName, relativePath)</returns>
        private static (string fileName, string relativePath) ExtractFileNameAndPath(string s3ObjectKey)
        {
            if (string.IsNullOrWhiteSpace(s3ObjectKey))
            {
                return (string.Empty, string.Empty);
            }

            // Normalize path separators to forward slashes (CloudFlare/S3 standard)
            var normalizedKey = s3ObjectKey.Replace("\\", "/");

            // Remove leading slash if present
            if (normalizedKey.StartsWith('/'))
            {
                normalizedKey = normalizedKey.Substring(1);
            }

            // Extract file name (everything after the last slash)
            var lastSlashIndex = normalizedKey.LastIndexOf('/');
            string fileName;
            string relativePath;

            if (lastSlashIndex == -1)
            {
                // No folder structure, file is in root
                fileName = normalizedKey;
                relativePath = string.Empty;
            }
            else
            {
                // File is in a folder structure
                fileName = normalizedKey.Substring(lastSlashIndex + 1);
                relativePath = normalizedKey.Substring(0, lastSlashIndex);
            }

            // Ensure we have a valid file name
            if (string.IsNullOrWhiteSpace(fileName))
            {
                throw new InvalidOperationException($"Invalid S3 object key: '{s3ObjectKey}' - could not extract file name");
            }

            return (fileName, relativePath);
        }

        public static string CombineUrl(string baseUrl, string subdomain, string relativePath, string fileName)
        {
            baseUrl = $"https://{subdomain}.{baseUrl}";

            relativePath = relativePath.TrimStart('/').TrimEnd('/');
            fileName = fileName.TrimStart('/').TrimEnd('/');
            return $"{baseUrl}/{relativePath}/{fileName}";
        }
    }
}