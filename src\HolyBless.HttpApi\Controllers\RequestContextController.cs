using System;
using HolyBless.Services;
using Microsoft.AspNetCore.Mvc;
using Volo.Abp.AspNetCore.Mvc;

namespace HolyBless.Controllers
{
    /// <summary>
    /// Demo controller to test RequestContextService functionality
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class RequestContextController : AbpControllerBase
    {
        private readonly IRequestContextService _requestContextService;

        public RequestContextController(IRequestContextService requestContextService)
        {
            _requestContextService = requestContextService;
        }

        /// <summary>
        /// Test endpoint to verify language headers are being read correctly
        /// </summary>
        /// <returns>Object containing the language codes from headers</returns>
        [HttpGet("language-info")]
        public IActionResult GetLanguageInfo()
        {
            var (languageCode, spokenLangCode) = _requestContextService.GetLanguageCodes();

            return Ok(new
            {
                LanguageCode = languageCode ?? "Not provided",
                SpokenLangCode = spokenLangCode ?? "Not provided",
                Message = "Language codes retrieved from request headers",
                Timestamp = DateTime.UtcNow
            });
        }

        /// <summary>
        /// Test endpoint that demonstrates conditional logic based on language headers
        /// </summary>
        /// <returns>Localized response based on language headers</returns>
        [HttpGet("localized-message")]
        public IActionResult GetLocalizedMessage()
        {
            var languageCode = _requestContextService.GetLanguageCode();
            var spokenLangCode = _requestContextService.GetSpokenLangCode();

            string message = languageCode?.ToLower() switch
            {
                "en-us" or "en" => "Hello! This is an English message.",
                "zh-cn" or "zh" => "你好！这是中文消息。",
                "es-es" or "es" => "¡Hola! Este es un mensaje en español.",
                "fr-fr" or "fr" => "Bonjour! Ceci est un message en français.",
                _ => "Hello! Default message (language not recognized)."
            };

            return Ok(new
            {
                Message = message,
                DetectedLanguage = languageCode,
                SpokenLanguage = spokenLangCode,
                SupportedLanguages = new[] { "en-US", "zh-CN", "es-ES", "fr-FR" }
            });
        }
    }
}