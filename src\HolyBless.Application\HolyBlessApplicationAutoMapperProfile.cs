using AutoMapper;
using HolyBless.Albums.Dtos;
using HolyBless.Articles;
using HolyBless.Articles.Dtos;
using HolyBless.Books.Dtos;
using HolyBless.Buckets;
using HolyBless.Buckets.Dtos;
using HolyBless.Channels.Dtos;
using HolyBless.Collections.Dtos;
using HolyBless.Entities.Albums;
using HolyBless.Entities.Articles;
using HolyBless.Entities.Books;
using HolyBless.Entities.Buckets;
using HolyBless.Entities.Channels;
using HolyBless.Entities.Collections;
using HolyBless.Entities.Tags;
using HolyBless.Lookups;
using HolyBless.Lookups.Dtos;
using HolyBless.StorageProviders;
using HolyBless.StorageProviders.Dtos;
using HolyBless.Tags.Dtos;
using System.Collections.Generic;
using Volo.Abp.AutoMapper;

namespace HolyBless;

public class HolyBlessApplicationAutoMapperProfile : Profile
{
    public HolyBlessApplicationAutoMapperProfile()
    {
        CreateMap<EBook, EBookDto>();
        CreateMap<CreateUpdateEBookDto, EBook>()
            .IgnoreFullAuditedObjectProperties()
            .Ignore(x => x.Channel)
            .Ignore(x => x.ThumbnailBucketFile)
            .Ignore(x => x.Chapters);

        CreateMap<Chapter, ChapterDto>();
        CreateMap<CreateUpdateChapterDto, Chapter>()
            .IgnoreFullAuditedObjectProperties()
            .Ignore(x => x.EBook)
            .Ignore(x => x.ParentChapter)
            .Ignore(x => x.ChildChapters)
            .Ignore(x => x.ChapterToArticles);

        CreateMap<ChapterToArticle, ChapterToArticleDto>()
            .ForMember(x => x.ChapterTitle, opt => opt.MapFrom(x => x.Chapter != null ? x.Chapter.Title : null))
            .ForMember(x => x.ArticleTitle, opt => opt.MapFrom(x => x.Article != null ? x.Article.Title : null));
        CreateMap<CreateUpdateChapterToArticleDto, ChapterToArticle>()
            .Ignore(x => x.Chapter)
            .Ignore(x => x.Article);

        // ChapterTreeDto mapping - manual mapping is handled in the service
        CreateMap<Chapter, ChapterTreeDto>()
            .ForMember(x => x.IsRoot, opt => opt.MapFrom(x => x.ParentChapterId == null))
            .ForMember(x => x.ArticleCount, opt => opt.Ignore())
            .ForMember(x => x.Children, opt => opt.Ignore());

        CreateMap<StorageProvider, StorageProviderDto>();
        CreateMap<CreateUpdateStorageProviderDto, StorageProvider>()
            .IgnoreFullAuditedObjectProperties()
            .Ignore(x => x.Buckets)
            .Ignore(x => x.Environment)
            .Ignore(x => x.PreferCountries)
            ;

        CreateMap<ProviderSecret, ProviderSecretDto>();
        CreateMap<CreateUpdateProviderSecretDto, ProviderSecret>()
            .IgnoreFullAuditedObjectProperties()
            ;

        CreateMap<StorageBucket, StorageBucketDto>()
            .ForMember(x => x.StorageName, opt => opt.MapFrom(x => x.StorageProvider.ProviderName));

        CreateMap<CreateUpdateStorageBucketDto, StorageBucket>()
            .IgnoreFullAuditedObjectProperties()
            .Ignore(x => x.StorageProvider)
            ;

        CreateMap<BucketFile, BucketFileDto>();
        CreateMap<CreateUpdateBucketFileDto, BucketFile>()
            .IgnoreFullAuditedObjectProperties()
            .Ignore(x => x.CollectionToFiles)
            .Ignore(x => x.FolderToBucketFiles)
            .Ignore(x => x.MediaType)
            .Ignore(x => x.Exists)
            ;

        // BucketFileUrl mappings
        CreateMap<BucketFileUrl, BucketFileUrlDto>();
        CreateMap<CreateUpdateBucketFileUrlDto, BucketFileUrl>()
            .IgnoreAuditedObjectProperties();

        CreateMap<Country, CountryDto>();
        CreateMap<CreateUpdateCountryDto, Country>()
            .IgnoreAuditedObjectProperties();

        CreateMap<Collection, CollectionDto>()
            ;
        CreateMap<Collection, CollectionTreeDto>()
            .ForMember(x => x.IsRoot, opt => opt.MapFrom(x => x.ParentCollectionId == null))
            .Ignore(x => x.Children);
        CreateMap<Collection, CollectionArticleTreeDto>()
            .ForMember(x => x.IsRoot, opt => opt.MapFrom(x => x.ParentCollectionId == null))
            .Ignore(x => x.Children)
            .Ignore(x => x.Articles);
        CreateMap<CreateUpdateCollectionDto, Collection>()
            .IgnoreFullAuditedObjectProperties()
            .Ignore(x => x.ContentCode)
            .Ignore(x => x.ThumbnailBucketFile)
            .Ignore(x => x.CollectionToArticles)
            .Ignore(x => x.CollectionToFiles)
            ;
        CreateMap<CollectionToFile, CollectionToFileDto>();
        CreateMap<CollectionToArticle, CollectionToArticleDto>();

        CreateMap<Channel, ChannelDto>()
            ;
        CreateMap<Channel, ChannelTreeDto>()
            .ForMember(x => x.IsRoot, opt => opt.MapFrom(x => x.ParentChannelId == null))
            .Ignore(x => x.Children);

        CreateMap<CreateUpdateChannelDto, Channel>()
            .IgnoreFullAuditedObjectProperties()
            .Ignore(x => x.ParentChannel)
            .Ignore(x => x.ChildChannels)
            .Ignore(x => x.VirtualDiskFolders)
            ;
        CreateMap<Article, ArticleDto>()
            .ForMember(x => x.ArticleFiles, opt => opt.MapFrom(src =>
                src.ArticleFiles != null ? src.ArticleFiles : new List<ArticleFile>()))
            .Ignore(x => x.ThumbnailUrl);

        CreateMap<CreateUpdateArticleDto, Article>()
            .IgnoreFullAuditedObjectProperties()
            .Ignore(x => x.ThumbnailBucketFile)
            .Ignore(x => x.CollectionToArticles)
            .Ignore(x => x.ArticleToTags)
            .Ignore(x => x.ArticleFiles);
        CreateMap<ArticleToTag, ArticleToTagDto>();

        CreateMap<ArticleFile, ArticleFileDto>()
            .ForMember(x => x.ArticleTitle, opt => opt.MapFrom(x => x.Article.Title))
            .ForMember(x => x.FileName, opt => opt.MapFrom(x => x.BucketFile.FileName))
            .ForMember(x => x.MediaType, opt => opt.MapFrom(x => x.BucketFile.MediaType))
            .ForMember(x => x.ContentType, opt => opt.MapFrom(x => x.BucketFile.ContentCategory));

        CreateMap<CreateUpdateArticleFileDto, ArticleFile>()
            .IgnoreFullAuditedObjectProperties()
            .Ignore(x => x.Article)
            .Ignore(x => x.BucketFile);

        CreateMap<Tag, TagDto>();
        CreateMap<CreateUpdateTagDto, Tag>()
            .IgnoreFullAuditedObjectProperties()
            .Ignore(x => x.ArticleToTags);

        // Album mappings
        CreateMap<Album, AlbumDto>()
            ;

        CreateMap<CreateUpdateAlbumDto, Album>()
            .IgnoreFullAuditedObjectProperties()
            .Ignore(x => x.Channel)
            .Ignore(x => x.ThumbnailBucketFile)
            .Ignore(x => x.AlbumToFiles)
            .Ignore(x => x.Views)
            .Ignore(x => x.Likes);

        // AlbumToFile mappings

        CreateMap<AlbumToFile, AlbumFileDto>()
            .ForMember(x => x.FileName, opt => opt.MapFrom(x => x.BucketFile.FileName))
            .ForMember(x => x.AlbumTitle, opt => opt.MapFrom(x => x.Album.Title))
            .ForMember(x => x.MediaType, opt => opt.MapFrom(x => x.BucketFile.MediaType))
           .ForMember(x => x.ContentCategory, opt => opt.MapFrom(x => x.BucketFile.ContentCategory))
           .ForMember(x => x.DeliveryDate, opt => opt.MapFrom(x => x.BucketFile.DeliveryDate))
            .Ignore(x => x.AlbumThumbnailUrl)
            .Ignore(x => x.FileUrl);

        CreateMap<CreateUpdateAlbumToFileDto, AlbumToFile>()
            .Ignore(x => x.Album)
            .Ignore(x => x.BucketFile)
            .Ignore(x => x.IsDeleted);
    }
}