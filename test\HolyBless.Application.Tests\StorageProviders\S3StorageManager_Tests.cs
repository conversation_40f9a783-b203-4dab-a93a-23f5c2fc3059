using System;
using Xunit;
using System.Reflection;

namespace HolyBless.StorageProviders
{
    public class S3StorageManager_Tests
    {
        [Theory]
        [InlineData("image.jpg", "image.jpg", "")]
        [InlineData("folder/image.jpg", "image.jpg", "folder")]
        [InlineData("folder/subfolder/image.jpg", "image.jpg", "folder/subfolder")]
        [InlineData("deep/nested/folder/structure/document.pdf", "document.pdf", "deep/nested/folder/structure")]
        [InlineData("/leading/slash/file.txt", "file.txt", "leading/slash")]
        [InlineData("folder\\with\\backslashes\\file.doc", "file.doc", "folder/with/backslashes")]
        [InlineData("mixed/folder\\structure/file.png", "file.png", "mixed/folder/structure")]
        [InlineData("chinese/中文文件夹/测试.txt", "测试.txt", "chinese/中文文件夹")]
        [InlineData("spaces in folder/file with spaces.pdf", "file with spaces.pdf", "spaces in folder")]
        public void ExtractFileNameAndPath_ShouldHandleVariousCloudFlarePathStructures(
            string s3ObjectKey, 
            string expectedFileName, 
            string expectedRelativePath)
        {
            // Act
            var result = CallPrivateExtractFileNameAndPath(s3ObjectKey);

            // Assert
            Assert.Equal(expectedFileName, result.fileName);
            Assert.Equal(expectedRelativePath, result.relativePath);
        }

        [Theory]
        [InlineData("")]
        [InlineData("   ")]
        [InlineData(null)]
        public void ExtractFileNameAndPath_ShouldHandleEmptyOrNullInput(string s3ObjectKey)
        {
            // Act
            var result = CallPrivateExtractFileNameAndPath(s3ObjectKey ?? string.Empty);

            // Assert
            Assert.Equal(string.Empty, result.fileName);
            Assert.Equal(string.Empty, result.relativePath);
        }

        [Theory]
        [InlineData("folder/")]
        [InlineData("folder/subfolder/")]
        public void ExtractFileNameAndPath_ShouldThrowForInvalidKeys(string s3ObjectKey)
        {
            // Act & Assert
            var exception = Assert.Throws<TargetInvocationException>(() => CallPrivateExtractFileNameAndPath(s3ObjectKey));
            Assert.IsType<InvalidOperationException>(exception.InnerException);
        }

        /// <summary>
        /// Helper method to call the private ExtractFileNameAndPath method using reflection
        /// </summary>
        private static (string fileName, string relativePath) CallPrivateExtractFileNameAndPath(string s3ObjectKey)
        {
            var method = typeof(S3StorageManager).GetMethod("ExtractFileNameAndPath", 
                BindingFlags.NonPublic | BindingFlags.Static);
            
            if (method == null)
            {
                throw new InvalidOperationException("ExtractFileNameAndPath method not found");
            }

            var result = method.Invoke(null, new object[] { s3ObjectKey });
            return ((string, string))result;
        }
    }
}
