<p-menubar [model]="menuItems">
  <!-- 左侧 Logo -->
  <ng-template pTemplate="start">
    <div
      class="flex align-items-center cursor-pointer"
      (click)="navigateToHome()"
    >
      <img src="assets/images/logo.svg" class="w-10" alt="" srcset="" />
    </div>
  </ng-template>

  <!-- 右侧控制区域 -->
  <ng-template pTemplate="end">
    <div class="control-area">
      <!-- 页面展示语言选择 -->
      <div class="language-selector">
        <i
          class="pi pi-language"
          tooltipPosition="bottom"
        ></i>
        <p-menu
          #menu2
          [model]="displayLanguages()"
          [popup]="true"
          appendTo="body"
        />
        <p-button
          (click)="menu2.toggle($event)"
          [label]="i18nService.currentLanguageInfo().label"
          [text]="true"
          size="small"
          class="language-button"
        />
      </div>

      <!-- 设置按钮 -->
      <p-button
        icon="pi pi-cog"
        [text]="true"
        [rounded]="true"
        severity="secondary"
        [pTooltip]="i18nService.translate('common.settings')"
        tooltipPosition="bottom"
        (onClick)="openSettings()"
      >
      </p-button>

      <p-button
        icon="pi pi-lightbulb"
        [text]="true"
        [rounded]="true"
        severity="secondary"
        [pTooltip]="i18nService.translate('common.settings')"
        tooltipPosition="bottom"
        (onClick)="themeService.toggleTheme()"
      >
      </p-button>

      <!-- 播放/暂停按钮 -->
      <p-button
        icon="pi pi-play"
        [text]="true"
        [rounded]="true"
        severity="primary"
        [pTooltip]="i18nService.translate('common.play')"
        tooltipPosition="bottom"
        (onClick)="togglePlay()"
      >
      </p-button>
    </div>
  </ng-template>
</p-menubar>
