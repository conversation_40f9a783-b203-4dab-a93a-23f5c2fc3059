using System.Collections.Generic;
using System.Threading.Tasks;
using HolyBless.Buckets.Dtos;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace HolyBless.Buckets
{
    public interface IBucketFileUrlAppService : ICrudAppService<
        BucketFileUrlDto,
        int,
        PagedAndSortedResultRequestDto,
        CreateUpdateBucketFileUrlDto>
    {
        Task<List<BucketFileUrlDto>> GetByBucketFileIdAsync(int bucketFileId);
        Task<BucketFileUrlDto?> GetByBucketFileIdAndProviderCodeAsync(int bucketFileId, string providerCode);
        Task<List<BucketFileUrlDto>> GetByProviderCodeAsync(string providerCode);
    }
}
