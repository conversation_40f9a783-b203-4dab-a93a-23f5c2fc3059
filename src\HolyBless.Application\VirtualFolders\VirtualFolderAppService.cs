﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using HolyBless.Entities.VirtualFolders;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Entities;
using Volo.Abp.Domain.Repositories;

namespace HolyBless.VirtualFolders
{
    public class VirtualFolderAppService : ReadOnlyVirtualFolderAppService, IVirtualFolderAppService
    {
        public VirtualFolderAppService(
             IRepository<VirtualDiskFolder, int> virtualFolderRepository,
             IRepository<FolderToFile> folderToBucketFileRepository,
             IRepository<VirtualDiskFolderTree, int> folderTreeRepository)
            : base(virtualFolderRepository, folderToBucketFileRepository, folderTreeRepository)
        {
        }

        public async Task UpdateFolderTreeJson(int rootFolderId)
        {
            var dbFolderTree = await _folderTreeRepository.GetAsync(rootFolderId);
            var folderTree = await GetFolderTreeWithFilesAsync(rootFolderId);
            dbFolderTree.TreeJsonData = System.Text.Json.JsonSerializer.Serialize(folderTree);
            await _folderTreeRepository.UpdateAsync(dbFolderTree);
        }

        public async Task SaveFolderTreeAsync(VirtualFolderTreeDto folderTreeDto, int? parentFolderId = null)
        {
            var folder = new VirtualDiskFolder
            {
                FolderName = folderTreeDto.FolderName,
                ParentFolderId = parentFolderId
            };
            folder = await _virtualFolderRepository.InsertAsync(folder, true);
            foreach (var fileDto in folderTreeDto.BucketFiles)
            {
                var mapping = new FolderToFile
                {
                    FolderId = folder.Id,
                    BucketFileId = fileDto.Id
                };
                await _folderToBucketFileRepository.InsertAsync(mapping, true);
            }
            foreach (var childFolderDto in folderTreeDto.Children)
            {
                await SaveFolderTreeAsync(childFolderDto, folder.Id);
            }
        }
    }
}