using HolyBless.Enums;
using System.ComponentModel.DataAnnotations;

namespace HolyBless.Albums.Dtos
{
    public class CreateUpdateAlbumDto
    {
        public int? ChannelId { get; set; }
        public int? ThumbnailFileId { get; set; }
        public string Title { get; set; } = "";
        public string? Description { get; set; }
        public int Weight { get; set; } = 0;
        public AlbumType AlbumType { get; set; }
        public string ContentCode { get; set; } = "";
        public string? LanguageCode { get; set; }
        public string? SpokenLangCode { get; set; }
    }
}
