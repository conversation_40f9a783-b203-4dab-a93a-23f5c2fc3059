.drawer-content {
  padding: 1rem;

  h4 {
    margin: 1rem 0 0.5rem 0;
    font-weight: 600;
    font-size: 1rem;

    &:first-child {
      margin-top: 0;
    }
  }

  .setting-item {
    margin-bottom: 1.5rem;

    label {
      font-weight: 500;
      font-size: 0.875rem;
      display: block;
      margin-bottom: 0.25rem;
    }

    p {
      font-size: 0.875rem;
      margin: 0;
      line-height: 1.4;
    }
  }

  .playlist-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem;
    border-radius: 0.375rem;
    cursor: pointer;
    transition: background-color 0.2s;
    position: relative;

    &:hover {
      background: #f9fafb;

      .remove-btn {
        opacity: 1;
      }
    }

    &.current {
      background: #f3f4f6;
      border: 1px solid #e5e7eb;

      .title {
        font-weight: 500;
      }

      .progress-info {
        margin-left: auto;
        
        small {
          color: #6b7280;
          font-size: 0.75rem;
        }
      }
    }

    i {
      color: #6b7280;
      flex-shrink: 0;

      &.text-green-500 {
        color: #10b981;
      }

      &.remove-btn {
        opacity: 0;
        transition: opacity 0.2s;
        cursor: pointer;
        margin-left: auto;
        padding: 0.25rem;

        &:hover {
          color: #ef4444;
        }
      }
    }

    .item-info {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 0.125rem;

      .title {
        font-size: 0.875rem;
        color: #374151;
      }

      .duration {
        font-size: 0.75rem;
        color: #9ca3af;
      }
    }
  }

  .playlist-items {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
    margin-bottom: 1rem;
  }

  .playlist-controls {
    border-top: 1px solid #e5e7eb;
    padding-top: 1rem;

    .clear-all-btn {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      width: 100%;
      padding: 0.5rem;
      background: transparent;
      border: 1px solid #d1d5db;
      border-radius: 0.375rem;
      color: #6b7280;
      font-size: 0.875rem;
      cursor: pointer;
      transition: all 0.2s;

      &:hover {
        background: #f9fafb;
        border-color: #9ca3af;
        color: #374151;
      }

      i {
        font-size: 0.75rem;
      }
    }
  }
}

// 自定义 PrimeNG drawer 样式
::ng-deep {
  .p-drawer {
    .p-drawer-header {
      padding: 1rem 1.5rem;

      .p-drawer-title {
        font-weight: 600;
        font-size: 1.125rem;
      }

      .p-drawer-header-icons {
        .p-drawer-header-icon {
          
          &:hover {
            background: #f3f4f6;
          }
        }
      }
    }

    .p-drawer-content {
      padding: 0;
    }
  }

  // 自定义遮罩层
  .p-drawer-mask {
    background-color: rgba(0, 0, 0, 0.4);
  }
}
