using System.Threading.Tasks;
using HolyBless.Enums;
using HolyBless.Lookups;
using Volo.Abp.Data;
using Volo.Abp.DependencyInjection;
using Volo.Abp.Domain.Repositories;

namespace HolyBless.DataSeeders
{
    public class CountryDataSeederContributor : IDataSeedContributor, ITransientDependency
    {
        private readonly IRepository<Country, int> _countryRepository;

        public static Country[] CountryList =>
            [
                new Country(1, "Afghanistan", "AFG","AF"),
                new Country(2, "Åland Islands", "ALA", "AX"),
                new Country(3, "Albania", "ALB", "AL"),
                new Country(4, "Algeria", "DZA", "DZ"),
                new Country(5, "American Samoa", "ASM", "AS"),
                new Country(6, "Andorra", "AND", "AD"),
                new Country(7, "Angola", "AGO", "AO"),
                new Country(8, "<PERSON>uilla", "AIA", "AI"),
                new Country(9, "Antarctica", "ATA", "AQ"),
                new Country(10, "Antigua and Barbuda", "ATG", "AG"),
                new Country(11, "Argentina", "ARG", "AR"),
                new Country(12, "Armenia", "ARM", "AM"),
                new Country(13, "Aruba", "ABW", "AW"),
                new Country(14, "Australia", "AUS", "AU"),
                new Country(15, "Austria", "AUT", "AT"),
                new Country(16, "Azerbaijan", "AZE", "AZ"),
                new Country(17, "Bahamas", "BHS", "BS"),
                new Country(18, "Bahrain", "BHR", "BH"),
                new Country(19, "Bangladesh", "BGD", "BD"),
                new Country(20, "Barbados", "BRB", "BB"),
                new Country(21, "Belarus", "BLR", "BY"),
                new Country(22, "Belgium", "BEL", "BE"),
                new Country(23, "Belize", "BLZ", "BZ"),
                new Country(24, "Benin", "BEN", "BJ"),
                new Country(25, "Bermuda", "BMU", "BM"),
                new Country(26, "Bhutan", "BTN", "BT"),
                new Country(27, "Bolivia, Plurinational State of", "BOL", "BO"),
                new Country(28, "Bonaire, Sint Eustatius and Saba", "BES", "BQ"),
                new Country(29, "Bosnia and Herzegovina", "BIH", "BA"),
                new Country(30, "Botswana", "BWA", "BW"),
                new Country(31, "Bouvet Island", "BVT", "BV"),
                new Country(32, "Brazil", "BRA", "BR"),
                new Country(33, "British Indian Ocean Territory", "IOT", "IO"),
                new Country(34, "Brunei Darussalam", "BRN", "BN"),
                new Country(35, "Bulgaria", "BGR", "BG"),
                new Country(36, "Burkina Faso", "BFA", "BF"),
                new Country(37, "Burundi", "BDI", "BI"),
                new Country(38, "Cabo Verde", "CPV", "CV"),
                new Country(39, "Cambodia", "KHM", "KH"),
                new Country(40, "Cameroon", "CMR", "CM"),
                new Country(41, "Canada", "CAN", "CA"),
                new Country(42, "Cayman Islands", "CYM","KY"),
                new Country(43, "Central African Republic", "CAF","CF"),
                new Country(44, "Chad", "TCD", "TD"),
                new Country(45, "Chile", "CHL", "CL"),
                new Country(46, "China", "CHN", "CN",LangCode.SimplifiedChinese,SpokenLangCode.Mandarin),
                new Country(47, "Christmas Island", "CXR", "CX"),
                new Country(48, "Cocos (Keeling) Islands", "CCK", "CC"),
                new Country(49, "Colombia", "COL", "CO"),
                new Country(50, "Comoros", "COM", "KM"),
                new Country(51, "Congo", "COG", "CG"),
                new Country(52, "Congo, the Democratic Republic of the", "COD", "CD"),
                new Country(53, "Cook Islands", "COK", "CK"),
                new Country(54, "Costa Rica", "CRI", "CR"),
                new Country(55, "Côte d'Ivoire", "CIV", "CI"),
                new Country(56, "Croatia", "HRV", "HR"),
                new Country(57, "Cuba", "CUB", "CU"),
                new Country(58, "Curaçao", "CUW", "CW"),
                new Country(59, "Cyprus", "CYP", "CY"),
                new Country(60, "Czech Republic", "CZE", "CZ"),
                new Country(61, "Denmark", "DNK", "DK"),
                new Country(62, "Djibouti", "DJI", "DJ"),
                new Country(63, "Dominica", "DMA", "DM"),
                new Country(64, "Dominican Republic", "DOM", "DO"),
                new Country(65, "Ecuador", "ECU", "EC"),
                new Country(66, "Egypt", "EGY", "EG"),
                new Country(67, "El Salvador", "SLV", "SV"),
                new Country(68, "Equatorial Guinea", "GNQ", "GQ"),
                new Country(69, "Eritrea", "ERI", "ER"),
                new Country(70, "Estonia", "EST", "EE"),
                new Country(71, "Ethiopia", "ETH", "ET" ),
                new Country(72, "Falkland Islands (Malvinas)", "FLK", "FK"),
                new Country(73, "Faroe Islands", "FRO", "FO"),
                new Country(74, "Fiji", "FJI", "FJ"),
                new Country(75, "Finland", "FIN", "FI"),
                new Country(76, "France", "FRA", "FR"),
                new Country(77, "French Guiana", "GUF", "GF"),
                new Country(78, "French Polynesia", "PYF", "PF"),
                new Country(79, "French Southern Territories", "ATF", "TF"),
                new Country(80, "Gabon", "GAB", "GA"),
                new Country(81, "Gambia", "GMB", "GM"),
                new Country(82, "Georgia", "GEO", "GE"),
                new Country(83, "Germany", "DEU", "DE"),
                new Country(84, "Ghana", "GHA", "GH"),
                new Country(85, "Gibraltar", "GIB", "GI"),
                new Country(86, "Greece", "GRC", "GR"),
                new Country(87, "Greenland", "GRL", "GL"),
                new Country(88, "Grenada", "GRD", "GD"),
                new Country(89, "Guadeloupe", "GLP", "GP"),
                new Country(90, "Guam", "GUM", "GU"),
                new Country(91, "Guatemala", "GTM", "GT"),
                new Country(92, "Guernsey", "GGY", "GG"),
                new Country(93, "Guinea", "GIN", "GN"),
                new Country(94, "Guinea-Bissau", "GNB", "GW"),
                new Country(95, "Guyana", "GUY", "GY"),
                new Country(96, "Haiti", "HTI", "HT"),
                new Country(97, "Heard Island and McDonald Islands", "HMD", "HM"),
                new Country(98, "Holy See (Vatican City State)", "VAT", "VA"),
                new Country(99, "Honduras", "HND", "HN"),
                new Country(100, "Hong Kong", "HKG", "HK", LangCode.TraditionalChinese,SpokenLangCode.Cantonese),
                new Country(101, "Hungary", "HUN", "HU"),
                new Country(102, "Iceland", "ISL", "IS"),
                new Country(103, "India", "IND", "IN"),
                new Country(104, "Indonesia", "IDN", "ID"),
                new Country(105, "Iran, Islamic Republic of", "IRN", "IR"),
                new Country(106, "Iraq", "IRQ", "IQ"),
                new Country(107, "Ireland", "IRL", "IE"),
                new Country(108, "Isle of Man", "IMN", "IM"),
                new Country(109, "Israel", "ISR", "IL"),
                new Country(110, "Italy", "ITA","IT"),
                new Country(111, "Jamaica", "JAM", "JM"),
                new Country(112, "Japan", "JPN", "JP"),
                new Country(113, "Jersey", "JEY", "JE"),
                new Country(114, "Jordan", "JOR", "JO"),
                new Country(115, "Kazakhstan", "KAZ", "KZ"),
                new Country(116, "Kenya", "KEN", "KE"),
                new Country(117, "Kiribati", "KIR", "KI"),
                new Country(118, "Korea, Democratic People's Republic of", "PRK", "KP"),
                new Country(119, "Korea, Republic of", "KOR", "KR"),
                new Country(120, "Kuwait", "KWT", "KW"),
                new Country(121, "Kyrgyzstan", "KGZ", "KG"),
                new Country(122, "Lao People's Democratic Republic", "LAO", "LA"),
                new Country(123, "Latvia", "LVA", "LV"),
                new Country(124, "Lebanon", "LBN", "LB"),
                new Country(125, "Lesotho", "LSO", "LS"),
                new Country(126, "Liberia", "LBR", "LR"),
                new Country(127, "Libya", "LBY", "LY"),
                new Country(128, "Liechtenstein", "LIE", "LI"),
                new Country(129, "Lithuania", "LTU", "LT"),
                new Country(130, "Luxembourg", "LUX", "LU"),
                new Country(131, "Macao", "MAC", "MO", LangCode.SimplifiedChinese,SpokenLangCode.Mandarin),
                new Country(132, "Macedonia, the former Yugoslav Republic of", "MKD","MK"),
                new Country(133, "Madagascar", "MDG", "MG"),
                new Country(134, "Malawi", "MWI", "MW"),
                new Country(135, "Malaysia", "MYS", "MY"),
                new Country(136, "Maldives", "MDV", "MV"),
                new Country(137, "Mali", "MLI", "ML"),
                new Country(138, "Malta", "MLT", "MT"),
                new Country(139, "Marshall Islands", "MHL", "MH"),
                new Country(140, "Martinique", "MTQ", "MQ"),
                new Country(141, "Mauritania", "MRT", "MR"),
                new Country(142, "Mauritius", "MUS","MU"),
                new Country(143, "Mayotte", "MYT", "YT"),
                new Country(144, "Mexico", "MEX", "MX"),
                new Country(145, "Micronesia, Federated States of", "FSM", "FM"),
                new Country(146, "Moldova, Republic of", "MDA", "MD"),
                new Country(147, "Monaco", "MCO", "MC"),
                new Country(148, "Mongolia", "MNG", "MN"),
                new Country(149, "Montenegro", "MNE","ME"),
                new Country(150, "Montserrat", "MSR", "MS"),
                new Country(151, "Morocco", "MAR", "MA"),
                new Country(152, "Mozambique", "MOZ", "MZ"),
                new Country(153, "Myanmar", "MMR", "MM"),
                new Country(154, "Namibia", "NAM", "NA"),
                new Country(155, "Nauru", "NRU","NR"),
                new Country(156, "Nepal", "NPL", "NP"),
                new Country(157, "Netherlands", "NLD", "NL"),
                new Country(158, "New Caledonia", "NCL", "NC"),
                new Country(159, "New Zealand", "NZL", "NZ"),
                new Country(160, "Nicaragua", "NIC", "NI"),
                new Country(161, "Niger", "NER", "NE"),
                new Country(162, "Nigeria", "NGA", "NG"),
                new Country(163, "Niue", "NIU", "NU"),
                new Country(164, "Norfolk Island", "NFK","NF"),
                new Country(165, "Northern Mariana Islands", "MNP", "MP"),
                new Country(166, "Norway", "NOR", "NO"),
                new Country(167, "Not Available", "N/A", "NA"),
                new Country(168, "Oman", "OMN", "OM"),
                new Country(169, "Other", "OTH", "OT"),
                new Country(170, "Pakistan", "PAK", "PK"),
                new Country(171, "Palau", "PLW", "PW"),
                new Country(172, "Palestine, State of", "PSE", "PS"),
                new Country(173, "Panama", "PAN", "PA"),
                new Country(174, "Papua New Guinea", "PNG", "PG"),
                new Country(175, "Paraguay", "PRY", "PY"),
                new Country(176, "Peru", "PER", "PE"),
                new Country(177, "Philippines", "PHL", "PH"),
                new Country(178, "Pitcairn", "PCN", "PN"),
                new Country(179, "Poland", "POL", "PL"),
                new Country(180, "Portugal", "PRT", "PT"),
                new Country(181, "Puerto Rico", "PRI", "PR"),
                new Country(182, "Qatar", "QAT", "QA"),
                new Country(183, "Réunion", "REU", "RE"),
                new Country(184, "Romania", "ROU", "RO"),
                new Country(185, "Russian Federation", "RUS", "RU"),
                new Country(186, "Rwanda", "RWA", "RW"),
                new Country(187, "Saint Barthélemy", "BLM","BL"),
                new Country(188, "Saint Helena, Ascension and Tristan da Cunha", "SHN", "SH"),
                new Country(189, "Saint Kitts and Nevis", "KNA", "KN"),
                new Country(190, "Saint Lucia", "LCA", "LC"),
                new Country(191, "Saint Martin (French part)", "MAF", "MF"),
                new Country(192, "Saint Pierre and Miquelon", "SPM", "PM"),
                new Country(193, "Saint Vincent and the Grenadines", "VCT","VC"),
                new Country(194, "Samoa", "WSM", "WS"),
                new Country(195, "San Marino", "SMR", "SM"),
                new Country(196, "Sao Tome and Principe", "STP", "ST"),
                new Country(197, "Saudi Arabia", "SAU", "SA"),
                new Country(198, "Senegal", "SEN", "SN"),
                new Country(199, "Serbia", "SRB", "RS"),
                new Country(200, "Seychelles", "SYC", "SC"),
                new Country(201, "Sierra Leone", "SLE", "SL"),
                new Country(202, "Singapore", "SGP", "SG"),
                new Country(203, "Sint Maarten (Dutch part)", "SXM", "SX"),
                new Country(204, "Slovakia", "SVK", "SK"),
                new Country(205, "Slovenia", "SVN", "SI"),
                new Country(206, "Solomon Islands", "SLB", "SB"),
                new Country(207, "Somalia", "SOM", "SO"),
                new Country(208, "South Africa", "ZAF", "ZA"),
                new Country(209, "South Georgia and the South Sandwich Islands", "SGS", "GS"),
                new Country(210, "South Sudan", "SSD", "SS"),
                new Country(211, "Spain", "ESP", "ES"),
                new Country(212, "Sri Lanka", "LKA", "LK"),
                new Country(213, "Sudan", "SDN", "SD"),
                new Country(214, "Suriname", "SUR", "SR"),
                new Country(215, "Svalbard and Jan Mayen", "SJM", "SJ"),
                new Country(216, "Swaziland", "SWZ", "SZ"),
                new Country(217, "Sweden", "SWE", "SE"),
                new Country(218, "Switzerland", "CHE", "CH"),
                new Country(219, "Syrian Arab Republic", "SYR", "SY"),
                new Country(220, "Taiwan, Province of China", "TWN", "TW", LangCode.TraditionalChinese,SpokenLangCode.Mandarin),
                new Country(221, "Tajikistan", "TJK", "TJ"),
                new Country(222, "Tanzania, United Republic of", "TZA", "TZ"),
                new Country(223, "Thailand", "THA", "TH"),
                new Country(224, "Timor-Leste", "TLS", "TL"),
                new Country(225, "Togo", "TGO","TG"),
                new Country(226, "Tokelau", "TKL", "TK"),
                new Country(227, "Tonga", "TON", "TO"),
                new Country(228, "Trinidad and Tobago", "TTO", "TT"),
                new Country(229, "Tunisia", "TUN", "TN"),
                new Country(230, "Turkey", "TUR", "TR"),
                new Country(231, "Turkmenistan", "TKM","TM"),
                new Country(232, "Turks and Caicos Islands", "TCA", "TC"),
                new Country(233, "Tuvalu", "TUV", "TV"),
                new Country(234, "Uganda", "UGA", "UG"),
                new Country(235, "Ukraine", "UKR", "UA"),
                new Country(236, "United Arab Emirates", "ARE", "AE"),
                new Country(237, "United Kingdom", "GBR", "GB"),
                new Country(238, "United States Minor Outlying Islands", "UMI", "UM"),
                new Country(239, "United States of America", "USA", "US"),
                new Country(240, "Uruguay", "URY", "UY"),
                new Country(241, "Uzbekistan", "UZB", "UZ"),
                new Country(242, "Vanuatu", "VUT", "VU"),
                new Country(243, "Venezuela, Bolivarian Republic of", "VEN", "VE"),
                new Country(244, "Viet Nam", "VNM", "VN"),
                new Country(245, "Virgin Islands, British", "VGB", "VG"),
                new Country(246, "Virgin Islands, U.S.", "VIR", "VI"),
                new Country(247, "Wallis and Futuna", "WLF", "WF"),
                new Country(248, "Western Sahara", "ESH", "EH"),
                new Country(249, "Yemen", "YEM","YE"),
                new Country(250, "Zambia", "ZMB", "ZM"),
                new Country(251, "Zimbabwe", "ZWE","ZW"),
            ];

        public CountryDataSeederContributor(IRepository<Country, int> countryRepository)
        {
            _countryRepository = countryRepository;
        }

        public async Task SeedAsync(DataSeedContext context)
        {
            var existing = await _countryRepository.GetListAsync();
            if (existing.Count > 0)
            {
                await _countryRepository.DeleteManyAsync(existing, autoSave: true);
            }
            //if (await _countryRepository.GetCountAsync() <= 0)
            {
                await _countryRepository.InsertManyAsync(
                    CountryList, autoSave: true
                );
            }
        }
    }
}